{"name": "checkpoint", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "cf:pages:preview": "wrangler pages dev .output/public --compatibility-date=2024-08-12 | cat", "db:migrate": "wrangler d1 migrations apply viablelab-checkpoint"}, "dependencies": {"nuxt": "^4.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1", "@pinia/nuxt": "^0.5.1", "pinia": "^2.2.6", "firebase": "^10.12.4", "jose": "^5.9.6", "shadcn-vue": "^2.2.0", "radix-vue": "^1.9.9", "tailwind-merge": "^2.5.3", "class-variance-authority": "^0.7.0"}, "devDependencies": {"@nuxtjs/tailwindcss": "^6.12.0", "autoprefixer": "^10.4.20", "postcss": "^8.4.41", "tailwindcss": "^3.4.10", "tailwindcss-animate": "^1.0.7", "wrangler": "^3.78.10", "typescript": "^5.5.4"}}