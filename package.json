{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^7.0.0", "@fortawesome/free-solid-svg-icons": "^7.0.0", "@fortawesome/vue-fontawesome": "^3.1.1", "@nuxtjs/tailwindcss": "^6.14.0", "autoprefixer": "^10.4.21", "firebase": "^12.1.0", "firebase-admin": "^13.4.0", "nuxt": "^4.0.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vue": "^3.5.18", "vue-router": "^4.5.1"}}