{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"firebase": "^12.1.0", "nuxt": "^4.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@nuxtjs/tailwindcss": "^6.14.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "lucide-vue-next": "^0.539.0", "postcss": "^8.5.6", "radix-vue": "^1.9.17", "shadcn-vue": "^2.2.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7"}}