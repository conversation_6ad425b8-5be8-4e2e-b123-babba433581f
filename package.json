{"name": "checkpoint-platform", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "db:generate": "wrangler d1 migrations apply viablelab-checkpoint --local", "db:deploy": "wrangler d1 migrations apply viablelab-checkpoint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-brands-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^3.0.3", "@nuxt/ui": "^3.0.0", "@pinia/nuxt": "^0.5.5", "firebase": "^10.12.0", "nuxt": "^4.0.3", "pinia": "^2.1.7", "tailwindcss": "^3.4.0", "uuid": "^9.0.1", "vue": "^3.5.18", "vue-router": "^4.5.1", "wrangler": "^3.57.0"}, "devDependencies": {"@types/uuid": "^9.0.8"}}