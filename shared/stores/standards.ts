import type { Standard } from '~/shared/types/models'

const SAMPLE: Standard[] = [
	{ id: 's1', code: '6.RP.A.3', title: 'Use ratio and rate reasoning to solve real-world problems' },
	{ id: 's2', code: '7.NS.A.1', title: 'Apply and extend previous understandings of operations with fractions' }
]

export function useStandards(){
	async function listStandards(): Promise<Standard[]>{
		const raw = localStorage.getItem('cp_standards')
		if(!raw){ localStorage.setItem('cp_standards', JSON.stringify(SAMPLE)) }
		return JSON.parse(localStorage.getItem('cp_standards') as string) as Standard[]
	}
	return { listStandards }
}

