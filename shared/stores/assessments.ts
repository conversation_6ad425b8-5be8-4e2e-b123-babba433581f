import type { Assessment } from '~/shared/types/models'

const LS_KEY = 'cp_assessments'

export function useAssessments(){
	async function listAssessments(): Promise<Assessment[]>{
		const raw = localStorage.getItem(LS_KEY)
		return raw ? JSON.parse(raw) as Assessment[] : []
	}

	async function saveAssessment(a: Assessment){
		const list = await listAssessments()
		list.push(a)
		localStorage.setItem(LS_KEY, JSON.stringify(list))
	}

	async function getAssessment(id: string){
		const list = await listAssessments()
		return list.find(x=>x.id===id)
	}

	async function updateAssessment(a: Assessment){
		const list = await listAssessments()
		const idx = list.findIndex(x=>x.id===a.id)
		if(idx>=0){ list[idx] = a }
		localStorage.setItem(LS_KEY, JSON.stringify(list))
	}

	return { listAssessments, saveAssessment, getAssessment, updateAssessment }
}

