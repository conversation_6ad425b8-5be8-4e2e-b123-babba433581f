import type { Misconception } from '~/shared/types/models'

const SAMPLE: Misconception[] = [
	{ id: 'm1', title: 'Confusing area and perimeter', description: 'Students mix up linear and square units.', standardIds: ['s1'] },
	{ id: 'm2', title: 'Misinterpreting unit rate', description: 'Fails to identify per-1 comparisons.', standardIds: ['s1'] },
	{ id: 'm3', title: 'Adding unlike fractions', description: 'Adds numerators and denominators directly.', standardIds: ['s2'] }
]

export function useMisconceptions(){
	async function listMisconceptions(): Promise<Misconception[]>{
		const raw = localStorage.getItem('cp_misconceptions')
		if(!raw){ localStorage.setItem('cp_misconceptions', JSON.stringify(SAMPLE)) }
		return JSON.parse(localStorage.getItem('cp_misconceptions') as string) as Misconception[]
	}
	return { listMisconceptions }
}

