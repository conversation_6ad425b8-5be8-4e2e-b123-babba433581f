import { getAuth, signInAnonymously, onAuthStateChanged } from 'firebase/auth'

export function useStudentAuth(){
	const auth = getAuth()
	const student = useState<any>('studentUser', ()=>null)
	onAuthStateChanged(auth, (u)=>{ student.value = u })

	async function signInAnonymouslyIfNeeded(){
		if(!auth.currentUser){
			await signInAnonymously(auth)
		}
	}

	async function getIdToken(){
		if(!auth.currentUser){ await signInAnonymouslyIfNeeded() }
		return await auth.currentUser!.getIdToken()
	}

	return { student, signInAnonymouslyIfNeeded, getIdToken }
}

