export type UserRole = 'teacher' | 'researcher'

export interface User {
  id: string
  firebaseUid: string
  email: string
  displayName?: string
  role: UserRole
  profilePictureUrl?: string
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  isActive: boolean
}

export interface AnonymousStudent {
  id: string
  firebaseUid: string
  createdAt: string
  lastSeenAt: string
}

export interface AuthState {
  user: User | null
  loading: boolean
  error: string | null
}