export type AssessmentItem = {
	id: string
	stem: string
	choices: string[]
	answerIndex: number
	misconceptionId?: string
	predicted?: { difficulty?: number; discrimination?: number }
}

export type Assessment = {
	id: string
	title: string
	subject: string
	grade: string
	standardId: string
	misconceptionIds: string[]
	items: AssessmentItem[]
	metrics?: { difficulty?: number; discrimination?: number }
	createdAt: number
}

export type Standard = { id: string; code: string; title: string; description?: string }
export type Misconception = { id: string; title: string; description: string; standardIds: string[] }

