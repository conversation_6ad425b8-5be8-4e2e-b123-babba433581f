import { initializeApp } from 'firebase/app'
import { getAuth, onAuthStateChanged, signInAnonymously } from 'firebase/auth'

export default defineNuxtPlugin((nuxtApp) => {
  const config = useRuntimeConfig()
  const firebaseConfig = config.public.firebase
  const app = initializeApp(firebaseConfig)
  const auth = getAuth(app)

  // Expose auth
  nuxtApp.provide('auth', auth)

  // Optionally auto sign-in anonymously for public routes
  onAuthStateChanged(auth, (user) => {
    if (!user) {
      // Do not await to avoid blocking
      signInAnonymously(auth).catch(() => {})
    }
  })
})


