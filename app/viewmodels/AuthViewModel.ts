// Auth ViewModel - State management and UI logic
import { 
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signInAnonymously,
  signOut,
  onAuthStateChanged,
  User
} from 'firebase/auth'
import type { UserRole } from '~/shared/types/auth'
import { AuthModel, type AuthUserModel, type UserProfileModel } from '~/models/AuthModel'

export class AuthViewModel {
  private firebaseAuth: any
  
  // Reactive state
  public user = ref<User | null>(null)
  public userProfile = ref<UserProfileModel | null>(null)
  public loading = ref(true)
  public error = ref<string | null>(null)

  constructor(firebaseAuth: any) {
    this.firebaseAuth = firebaseAuth
    this.initializeAuthListener()
  }

  private initializeAuthListener() {
    onAuthStateChanged(this.firebaseAuth, async (firebaseUser) => {
      this.user.value = firebaseUser
      
      if (firebaseUser) {
        try {
          this.userProfile.value = await AuthModel.getUserProfile(firebaseUser.uid)
        } catch (err) {
          console.warn('Failed to load user profile:', err)
          this.userProfile.value = null
        }
      } else {
        this.userProfile.value = null
      }
      
      this.loading.value = false
    })
  }

  async signInWithEmail(email: string, password: string) {
    try {
      this.error.value = null
      this.loading.value = true
      const result = await signInWithEmailAndPassword(this.firebaseAuth, email, password)
      return result
    } catch (err: any) {
      this.error.value = err.message
      throw err
    } finally {
      this.loading.value = false
    }
  }

  async createAccountWithEmail(email: string, password: string, displayName: string, role: UserRole = 'teacher') {
    try {
      this.error.value = null
      this.loading.value = true
      const result = await createUserWithEmailAndPassword(this.firebaseAuth, email, password)
      
      await AuthModel.createUser({
        firebaseUid: result.user.uid,
        email: result.user.email!,
        displayName,
        role
      })
      
      return result
    } catch (err: any) {
      this.error.value = err.message
      throw err
    } finally {
      this.loading.value = false
    }
  }

  async signInWithGoogle(role: UserRole = 'teacher') {
    try {
      this.error.value = null
      this.loading.value = true
      const provider = new GoogleAuthProvider()
      const result = await signInWithPopup(this.firebaseAuth, provider)
      
      await AuthModel.createUser({
        firebaseUid: result.user.uid,
        email: result.user.email!,
        displayName: result.user.displayName || undefined,
        profilePictureUrl: result.user.photoURL || undefined,
        role
      })
      
      return result
    } catch (err: any) {
      this.error.value = err.message
      throw err
    } finally {
      this.loading.value = false
    }
  }

  async signInAnonymous() {
    try {
      this.error.value = null
      this.loading.value = true
      const result = await signInAnonymously(this.firebaseAuth)
      
      await AuthModel.createAnonymousStudent(result.user.uid)
      
      return result
    } catch (err: any) {
      this.error.value = err.message
      throw err
    } finally {
      this.loading.value = false
    }
  }

  async logout() {
    try {
      await signOut(this.firebaseAuth)
      await navigateTo('/')
    } catch (err: any) {
      this.error.value = err.message
      throw err
    }
  }

  // Computed properties for UI
  get isTeacher() {
    return this.user.value && !this.user.value.isAnonymous
  }

  get isStudent() {
    return this.user.value && this.user.value.isAnonymous
  }

  get userRole(): UserRole | null {
    return this.userProfile.value?.role || null
  }
}