<template>
	<div>
		<div v-if="!started" class="text-center py-20">
			<h1 class="text-2xl font-semibold text-slate-900">Waiting for the teacher to start…</h1>
			<p class="mt-2 text-slate-600">Participants:</p>
			<ul class="mt-4 inline-flex flex-wrap gap-2 justify-center">
				<li v-for="(n,idx) in participants" :key="idx" class="px-3 py-1 rounded-full bg-slate-100 text-slate-700 text-sm">{{ n }}</li>
			</ul>
		</div>
		<div v-else class="max-w-3xl mx-auto py-10">
			<div class="mb-6 text-sm text-slate-600">Question {{ currentIndex+1 }} of {{ questions.length }}</div>
			<div v-for="(q, qi) in questions" :key="q.id" class="mb-8" v-show="mode==='all' || qi===currentIndex">
				<div class="bg-white border border-slate-200 rounded-xl p-6">
					<div class="font-medium text-slate-900">{{ q.stem }}</div>
					<div class="mt-4 grid gap-3">
						<button v-for="(c, ci) in q.choices" :key="ci" @click="select(q.id, ci)" :class="['text-left px-4 py-3 rounded-md border', selected[q.id]===ci ? 'border-orange-500 bg-orange-50' : 'border-slate-300 hover:bg-slate-50']">{{ c }}</button>
					</div>
				</div>
			</div>
			<div class="flex items-center justify-between">
				<button v-if="mode==='one'" class="px-4 py-2 rounded-md border" @click="prev" :disabled="currentIndex===0">Prev</button>
				<div class="flex-1"></div>
				<button v-if="mode==='one'" class="px-4 py-2 rounded-md border" @click="next" :disabled="currentIndex===questions.length-1">Next</button>
			</div>
			<div class="mt-6 text-center">
				<button class="px-6 py-3 rounded-md bg-orange-500 text-white hover:bg-orange-600" @click="submit" :disabled="submitted">Submit Answers</button>
			</div>
		</div>
		<div v-if="submitted" class="text-center py-20">
			<h2 class="text-2xl font-semibold text-slate-900">Great job! Your answers have been submitted.</h2>
		</div>
	</div>
</template>

<script setup lang="ts">
definePageMeta({ layout: 'student' })

const route = useRoute()
const sessionId = computed(()=>route.params.sessionId as string)
const started = ref(false)
const participants = ref<string[]>([])
const mode = ref<'one'|'all'>('one')
const currentIndex = ref(0)
const questions = ref<{ id:string; stem:string; choices:string[] }[]>([])
const selected = ref<Record<string, number>>({})
const submitted = ref(false)

onMounted(async()=>{
	const data = await $fetch(`/api/session/${sessionId.value}`)
	started.value = (data as any).started
	participants.value = (data as any).participants||[]
	questions.value = (data as any).questions||[]
})

function select(qid: string, choiceIndex: number){
	selected.value[qid] = choiceIndex
}

function next(){ if(currentIndex.value<questions.value.length-1) currentIndex.value++ }
function prev(){ if(currentIndex.value>0) currentIndex.value-- }

async function submit(){
	const { getIdToken } = useStudentAuth()
	const idToken = await getIdToken()
	await $fetch(`/api/session/${sessionId.value}/submit`, { method: 'POST', body: { idToken, answers: selected.value } })
	submitted.value = true
}
</script>

