<template>
  <div class="max-w-6xl mx-auto">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center h-64">
      <div class="text-center">
        <font-awesome-icon icon="spinner" spin class="text-primary-500 text-3xl mb-4" />
        <p class="text-secondary-600">Loading session...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex items-center justify-center h-64">
      <div class="text-center">
        <font-awesome-icon icon="times" class="text-red-500 text-3xl mb-4" />
        <h3 class="text-xl font-semibold text-secondary-800 mb-2">Session Error</h3>
        <p class="text-secondary-600 mb-6">{{ error }}</p>
        <NuxtLink
          to="/dashboard"
          class="bg-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Return to Dashboard
        </NuxtLink>
      </div>
    </div>

    <!-- Main Host Interface -->
    <div v-else class="space-y-8">
      <!-- Session Info Card -->
      <div class="bg-gradient-to-r from-primary-600 to-accent-500 rounded-2xl p-8 text-white">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold mb-2">{{ session.assessment_title }}</h1>
            <p class="text-primary-100 text-lg">Session is {{ session.status }}</p>
          </div>
          <div class="text-center">
            <div class="text-6xl font-bold tracking-wider bg-white bg-opacity-20 rounded-xl px-6 py-4 mb-2">
              {{ session.join_code }}
            </div>
            <p class="text-primary-100">Join Code</p>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Start/Control Session -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center">
              <font-awesome-icon :icon="session.status === 'waiting' ? 'play' : 'pause'" class="text-accent-600 text-xl" />
            </div>
            <h3 class="text-lg font-semibold text-secondary-800">Session Control</h3>
          </div>
          
          <button
            v-if="session.status === 'waiting'"
            @click="startSession"
            :disabled="participants.length === 0"
            class="w-full bg-accent-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-accent-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
          >
            <font-awesome-icon icon="play" />
            <span>Start Assessment</span>
          </button>
          
          <button
            v-else-if="session.status === 'active'"
            @click="endSession"
            class="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-700 transition-colors flex items-center justify-center space-x-2"
          >
            <font-awesome-icon icon="times" />
            <span>End Session</span>
          </button>
          
          <div v-else class="text-center text-secondary-600">
            <font-awesome-icon icon="check" class="text-2xl mb-2" />
            <p>Session Completed</p>
          </div>
        </div>

        <!-- Join Instructions -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
              <font-awesome-icon icon="external-link-alt" class="text-primary-600 text-xl" />
            </div>
            <h3 class="text-lg font-semibold text-secondary-800">Join Instructions</h3>
          </div>
          
          <div class="space-y-3">
            <div class="p-3 bg-secondary-50 rounded-lg">
              <p class="text-sm text-secondary-700 mb-1">Direct Link:</p>
              <div class="flex items-center space-x-2">
                <input
                  :value="joinUrl"
                  readonly
                  class="flex-1 text-sm bg-white border border-secondary-300 rounded px-2 py-1"
                />
                <button
                  @click="copyUrl"
                  class="p-1 text-primary-600 hover:text-primary-700"
                  title="Copy Link"
                >
                  <font-awesome-icon icon="copy" />
                </button>
              </div>
            </div>
            
            <div class="text-center text-sm text-secondary-600">
              Or visit <strong>{{ baseUrl }}</strong> and enter code <strong>{{ session.join_code }}</strong>
            </div>
          </div>
        </div>

        <!-- View Report -->
        <div class="bg-white rounded-xl shadow-lg p-6">
          <div class="flex items-center space-x-3 mb-4">
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <font-awesome-icon icon="chart-bar" class="text-green-600 text-xl" />
            </div>
            <h3 class="text-lg font-semibold text-secondary-800">Results</h3>
          </div>
          
          <button
            v-if="session.status === 'finished'"
            @click="viewReport"
            class="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center justify-center space-x-2"
          >
            <font-awesome-icon icon="chart-bar" />
            <span>View Report</span>
          </button>
          
          <div v-else class="text-center text-secondary-400">
            <font-awesome-icon icon="chart-bar" class="text-2xl mb-2 opacity-50" />
            <p class="text-sm">Report available after session ends</p>
          </div>
        </div>
      </div>

      <!-- Participants List -->
      <div class="bg-white rounded-xl shadow-lg">
        <div class="p-6 border-b border-secondary-200">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-bold text-secondary-800 flex items-center space-x-2">
              <font-awesome-icon icon="users" />
              <span>Participants ({{ participants.length }})</span>
            </h2>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span class="text-sm text-secondary-600">Live updates</span>
            </div>
          </div>
        </div>

        <div class="p-6">
          <div v-if="participants.length === 0" class="text-center py-12">
            <font-awesome-icon icon="users" class="text-4xl text-secondary-300 mb-4" />
            <h3 class="text-lg font-medium text-secondary-600 mb-2">Waiting for participants...</h3>
            <p class="text-secondary-500">Share the join code <strong>{{ session.join_code }}</strong> with your students</p>
          </div>

          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <div
              v-for="participant in participants"
              :key="participant.id"
              class="bg-secondary-50 rounded-lg p-4 flex items-center space-x-3"
            >
              <div class="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center">
                <span class="text-white font-semibold text-sm">
                  {{ participant.nickname.charAt(0).toUpperCase() }}
                </span>
              </div>
              <div class="flex-1">
                <p class="font-medium text-secondary-800">{{ participant.nickname }}</p>
                <div class="flex items-center space-x-2 text-xs text-secondary-500">
                  <span>{{ formatTime(participant.created_at) }}</span>
                  <div v-if="session.status === 'active'" class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span>Active</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Session Progress (for active sessions) -->
      <div v-if="session.status === 'active'" class="bg-white rounded-xl shadow-lg">
        <div class="p-6 border-b border-secondary-200">
          <h2 class="text-xl font-bold text-secondary-800">Session Progress</h2>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="text-center">
              <div class="text-3xl font-bold text-primary-600">{{ session.total_questions || 0 }}</div>
              <div class="text-sm text-secondary-600">Total Questions</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-accent-600">{{ responseCount }}</div>
              <div class="text-sm text-secondary-600">Responses Received</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-green-600">
                {{ participants.length > 0 ? Math.round((responseCount / (participants.length * (session.total_questions || 1))) * 100) : 0 }}%
              </div>
              <div class="text-sm text-secondary-600">Completion Rate</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Success Message -->
    <div v-if="message" class="fixed bottom-6 right-6 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg z-50">
      {{ message }}
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'teacher',
  middleware: 'auth'
});

const route = useRoute();
const sessionId = route.params.id as string;

// State
const session = ref<any>({});
const participants = ref<any[]>([]);
const responseCount = ref(0);
const isLoading = ref(true);
const error = ref('');
const message = ref('');

// Computed
const joinUrl = computed(() => `${baseUrl.value}/session/${sessionId}/play`);
const baseUrl = computed(() => {
  if (process.client) {
    return `${window.location.protocol}//${window.location.host}`;
  }
  return 'https://checkpoint.com'; // fallback
});

// Load session data
onMounted(async () => {
  await loadSessionData();
  startPolling();
});

const loadSessionData = async () => {
  try {
    const [sessionData, participantsData] = await Promise.all([
      $fetch(`/api/sessions/${sessionId}`),
      $fetch(`/api/sessions/${sessionId}/participants`)
    ]);
    
    session.value = sessionData;
    participants.value = participantsData || [];
    
    // Get response count if session is active
    if (session.value.status === 'active' || session.value.status === 'finished') {
      const stats = await $fetch(`/api/sessions/${sessionId}/stats`);
      responseCount.value = stats.total_responses || 0;
    }
  } catch (err: any) {
    error.value = err.status === 404 
      ? 'Session not found or you do not have permission to access it.'
      : 'Failed to load session data.';
  } finally {
    isLoading.value = false;
  }
};

const startPolling = () => {
  const interval = setInterval(async () => {
    try {
      const [updatedSession, updatedParticipants] = await Promise.all([
        $fetch(`/api/sessions/${sessionId}`),
        $fetch(`/api/sessions/${sessionId}/participants`)
      ]);
      
      session.value = { ...session.value, ...updatedSession };
      participants.value = updatedParticipants || [];
      
      if (session.value.status === 'active' || session.value.status === 'finished') {
        const stats = await $fetch(`/api/sessions/${sessionId}/stats`);
        responseCount.value = stats.total_responses || 0;
      }
      
      // Stop polling if session is finished
      if (session.value.status === 'finished') {
        clearInterval(interval);
      }
    } catch (err) {
      // Continue polling even if there's an error
    }
  }, 3000);

  onUnmounted(() => {
    clearInterval(interval);
  });
};

const startSession = async () => {
  try {
    await $fetch(`/api/sessions/${sessionId}/start`, {
      method: 'POST'
    });
    
    session.value.status = 'active';
    showMessage('Assessment started successfully!');
  } catch (error) {
    console.error('Failed to start session:', error);
  }
};

const endSession = async () => {
  if (!confirm('Are you sure you want to end this session? This cannot be undone.')) {
    return;
  }
  
  try {
    await $fetch(`/api/sessions/${sessionId}/end`, {
      method: 'POST'
    });
    
    session.value.status = 'finished';
    showMessage('Assessment ended successfully!');
  } catch (error) {
    console.error('Failed to end session:', error);
  }
};

const copyUrl = async () => {
  try {
    await navigator.clipboard.writeText(joinUrl.value);
    showMessage('Link copied to clipboard!');
  } catch (error) {
    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = joinUrl.value;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    showMessage('Link copied to clipboard!');
  }
};

const viewReport = () => {
  navigateTo(`/report/${sessionId}`);
};

const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

const showMessage = (text: string) => {
  message.value = text;
  setTimeout(() => {
    message.value = '';
  }, 3000);
};

useHead({
  title: computed(() => `Host: ${session.value.assessment_title || 'Session'} - CHECKPOINT`)
});
</script>