<template>
  <div class="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100">
    <!-- Waiting Screen -->
    <div v-if="sessionStatus === 'waiting'" class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <div class="bg-white rounded-3xl shadow-2xl p-12 max-w-md mx-auto">
          <div class="w-20 h-20 bg-gradient-to-br from-primary-500 to-accent-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <FontAwesomeIcon icon="users" class="text-white text-2xl" />
          </div>
          
          <h2 class="text-2xl font-bold text-secondary-800 mb-4">Welcome, {{ participantName }}!</h2>
          <p class="text-secondary-600 mb-8">You've successfully joined the assessment. Wait for your teacher to start.</p>
          
          <div class="flex items-center justify-center space-x-3 text-primary-600">
            <FontAwesomeIcon icon="spinner" spin />
            <span class="font-medium">Waiting for teacher to begin...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Assessment Playing -->
    <div v-else-if="sessionStatus === 'active' && currentItem" class="min-h-screen flex flex-col">
      <!-- Progress Bar -->
      <div class="bg-white shadow-sm border-b border-secondary-200 p-4">
        <div class="container mx-auto">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-secondary-600">Question {{ currentIndex + 1 }} of {{ totalItems }}</span>
            <span class="text-sm font-medium text-secondary-600">{{ participantName }}</span>
          </div>
          <div class="w-full bg-secondary-200 rounded-full h-3">
            <div 
              class="bg-gradient-to-r from-primary-500 to-accent-500 h-3 rounded-full transition-all duration-500"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
        </div>
      </div>

      <!-- Question Content -->
      <div class="flex-1 container mx-auto px-4 py-8 flex items-center justify-center">
        <div class="w-full max-w-4xl">
          <div class="bg-white rounded-3xl shadow-2xl p-8 md:p-12">
            <!-- Question -->
            <div class="mb-12">
              <h1 class="text-2xl md:text-3xl font-bold text-secondary-800 leading-relaxed">
                {{ currentItem.content.question }}
              </h1>
            </div>

            <!-- Options -->
            <div class="space-y-4">
              <button
                v-for="(option, index) in currentItem.content.options"
                :key="index"
                @click="selectAnswer(index)"
                :disabled="isSubmitting || hasAnswered"
                :class="[
                  'w-full p-6 rounded-2xl border-2 text-left transition-all duration-200 font-medium text-lg',
                  selectedAnswer === index 
                    ? 'border-primary-500 bg-primary-50 text-primary-800' 
                    : 'border-secondary-300 hover:border-primary-300 hover:bg-primary-25 text-secondary-700',
                  (isSubmitting || hasAnswered) && 'cursor-not-allowed opacity-75'
                ]"
              >
                <div class="flex items-center space-x-4">
                  <div :class="[
                    'w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold',
                    selectedAnswer === index 
                      ? 'border-primary-500 bg-primary-500 text-white' 
                      : 'border-secondary-400 text-secondary-600'
                  ]">
                    {{ String.fromCharCode(65 + index) }}
                  </div>
                  <span>{{ option.text }}</span>
                </div>
              </button>
            </div>

            <!-- Submit Button -->
            <div class="mt-12 text-center">
              <button
                v-if="selectedAnswer !== null && !hasAnswered"
                @click="submitAnswer"
                :disabled="isSubmitting"
                class="bg-gradient-to-r from-primary-600 to-accent-500 text-white py-4 px-12 rounded-xl text-lg font-semibold hover:from-primary-700 hover:to-accent-600 focus:outline-none focus:ring-4 focus:ring-primary-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
              >
                <FontAwesomeIcon v-if="isSubmitting" icon="spinner" spin class="mr-3" />
                {{ isSubmitting ? 'Submitting...' : 'Submit Answer' }}
              </button>

              <!-- Waiting for next question -->
              <div v-if="hasAnswered" class="flex items-center justify-center space-x-3 text-primary-600">
                <FontAwesomeIcon icon="check" class="text-2xl" />
                <span class="text-lg font-medium">Answer submitted! Waiting for next question...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Completion Screen -->
    <div v-else-if="sessionStatus === 'finished'" class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <div class="bg-white rounded-3xl shadow-2xl p-12 max-w-md mx-auto">
          <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-primary-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <FontAwesomeIcon icon="check" class="text-white text-3xl" />
          </div>
          
          <h2 class="text-2xl font-bold text-secondary-800 mb-4">Assessment Complete!</h2>
          <p class="text-secondary-600 mb-8">Thank you for participating, {{ participantName }}. Your responses have been recorded.</p>
          
          <button
            @click="exitSession"
            class="bg-gradient-to-r from-primary-600 to-accent-500 text-white py-3 px-8 rounded-xl font-semibold hover:from-primary-700 hover:to-accent-600 focus:outline-none focus:ring-4 focus:ring-primary-300 transition-all duration-200"
          >
            Exit Assessment
          </button>
        </div>
      </div>
    </div>

    <!-- Error Screen -->
    <div v-else-if="error" class="min-h-screen flex items-center justify-center">
      <div class="text-center">
        <div class="bg-white rounded-3xl shadow-2xl p-12 max-w-md mx-auto">
          <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <FontAwesomeIcon icon="times" class="text-red-500 text-2xl" />
          </div>
          
          <h2 class="text-2xl font-bold text-secondary-800 mb-4">Something went wrong</h2>
          <p class="text-secondary-600 mb-8">{{ error }}</p>
          
          <NuxtLink
            to="/"
            class="inline-block bg-gradient-to-r from-primary-600 to-accent-500 text-white py-3 px-8 rounded-xl font-semibold hover:from-primary-700 hover:to-accent-600 transition-all duration-200"
          >
            Return to Home
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const sessionId = route.params.id as string;

// State
const sessionStatus = ref<'waiting' | 'active' | 'finished'>('waiting');
const items = ref<any[]>([]);
const currentIndex = ref(0);
const selectedAnswer = ref<number | null>(null);
const hasAnswered = ref(false);
const isSubmitting = ref(false);
const participantId = ref<number | null>(null);
const participantName = ref('');
const error = ref('');

// Computed
const currentItem = computed(() => items.value[currentIndex.value]);
const totalItems = computed(() => items.value.length);
const progress = computed(() => totalItems.value > 0 ? ((currentIndex.value + 1) / totalItems.value) * 100 : 0);

// Initialize
onMounted(async () => {
  try {
    // Get participant info from session storage
    const nickname = sessionStorage.getItem('participantNickname');
    const joinCode = sessionStorage.getItem('joinCode');

    if (!nickname || !joinCode) {
      throw new Error('Missing participant information');
    }

    participantName.value = nickname;

    // Join the session
    const response = await $fetch(`/api/sessions/join/${joinCode}`, {
      method: 'POST',
      body: { nickname }
    });

    if (response.participant_id && response.assessment_items) {
      participantId.value = response.participant_id;
      items.value = response.assessment_items;
      sessionStatus.value = 'active';
    }

    // Start polling for session updates (in real implementation, use WebSockets)
    startPolling();
    
  } catch (err: any) {
    error.value = err.message || 'Failed to join session';
  }
});

const startPolling = () => {
  // Poll for session status updates every 2 seconds
  const interval = setInterval(async () => {
    try {
      const sessionData = await $fetch(`/api/sessions/${sessionId}/status`);
      if (sessionData.status !== sessionStatus.value) {
        sessionStatus.value = sessionData.status;
        if (sessionStatus.value === 'finished') {
          clearInterval(interval);
        }
      }
    } catch (err) {
      // Continue polling even if there's an error
    }
  }, 2000);

  // Clean up on unmount
  onUnmounted(() => {
    clearInterval(interval);
  });
};

const selectAnswer = (index: number) => {
  if (hasAnswered.value || isSubmitting.value) return;
  selectedAnswer.value = index;
};

const submitAnswer = async () => {
  if (selectedAnswer.value === null || hasAnswered.value || !participantId.value) return;

  isSubmitting.value = true;
  try {
    await $fetch('/api/responses', {
      method: 'POST',
      body: {
        participant_id: participantId.value,
        item_id: currentItem.value.id,
        response_data: {
          answer_index: selectedAnswer.value,
          text: currentItem.value.content.options[selectedAnswer.value].text
        }
      }
    });

    hasAnswered.value = true;
    
    // Auto advance after 2 seconds
    setTimeout(() => {
      nextQuestion();
    }, 2000);

  } catch (err) {
    error.value = 'Failed to submit answer. Please try again.';
  } finally {
    isSubmitting.value = false;
  }
};

const nextQuestion = () => {
  if (currentIndex.value < totalItems.value - 1) {
    currentIndex.value++;
    selectedAnswer.value = null;
    hasAnswered.value = false;
  } else {
    sessionStatus.value = 'finished';
  }
};

const exitSession = () => {
  sessionStorage.removeItem('participantNickname');
  sessionStorage.removeItem('joinCode');
  navigateTo('/');
};

// Set page title
useHead({
  title: 'Assessment in Progress - CHECKPOINT'
});

definePageMeta({
  layout: false
});
</script>