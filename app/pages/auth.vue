<template>
	<section class="max-w-xl mx-auto py-16">
		<div class="bg-white border border-slate-200 shadow-sm rounded-xl p-6">
			<h1 class="text-xl font-semibold text-slate-900">Teacher Sign In</h1>
			<form class="mt-6 space-y-4" @submit.prevent="emailSignIn">
				<label class="block">
					<span class="text-sm text-slate-600">Email</span>
					<input v-model="email" type="email" class="mt-1 w-full px-4 py-3 rounded-md border border-slate-300" />
				</label>
				<label class="block">
					<span class="text-sm text-slate-600">Password</span>
					<input v-model="password" type="password" class="mt-1 w-full px-4 py-3 rounded-md border border-slate-300" />
				</label>
				<button type="submit" class="w-full px-5 py-3 rounded-md bg-slate-900 text-white">Sign in</button>
			</form>
			<div class="mt-6">
				<button class="w-full px-5 py-3 rounded-md border" @click="googleSignIn"><i class="fa-brands fa-google mr-2"></i> Continue with Google</button>
			</div>
		</div>
	</section>
</template>

<script setup lang="ts">
const email = ref('')
const password = ref('')
const teacherUser = useState<any>('teacherUser')
const router = useRouter()

onMounted(()=>{
	if(teacherUser.value){ router.push('/app/dashboard') }
})

async function emailSignIn(){
	// For demo, auto sign in without backend. In production, use signInWithEmailAndPassword
	teacherUser.value = { email: email.value }
	router.push('/app/dashboard')
}

async function googleSignIn(){
	const { $auth, $GoogleAuthProvider, $signInWithPopup } = useNuxtApp() as any
	try{
		const provider = new $GoogleAuthProvider()
		const res = await $signInWithPopup($auth, provider)
		teacherUser.value = res.user
		router.push('/app/dashboard')
	}catch(e){
		console.error(e)
	}
}
</script>

