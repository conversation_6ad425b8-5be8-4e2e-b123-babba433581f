<template>
	<section>
		<div class="relative isolate overflow-hidden">
			<div class="mx-auto max-w-7xl px-6 py-20">
				<div class="grid md:grid-cols-2 gap-12 items-center">
					<div>
						<h1 class="text-4xl md:text-5xl font-semibold tracking-tight text-slate-900">Pinpoint Misconceptions. Elevate Learning.</h1>
						<p class="mt-4 text-lg text-slate-600">AI-powered assessments that give you actionable insights in minutes.</p>
						<div class="mt-8 flex items-center gap-4">
							<NuxtLink to="/app/dashboard" class="px-5 py-3 rounded-md bg-slate-900 text-white hover:bg-slate-800">Get Started for Free</NuxtLink>
							<NuxtLink to="/app/library" class="px-5 py-3 rounded-md border border-slate-300 hover:bg-slate-100">See Library</NuxtLink>
						</div>
					</div>
					<div class="bg-white border border-slate-200 shadow-sm rounded-xl p-6">
						<div class="flex items-center justify-between">
							<h2 class="font-medium text-slate-900">Join a Session</h2>
							<i class="fa-solid fa-person-chalkboard text-slate-400"></i>
						</div>
						<form class="mt-4 flex items-center gap-3" @submit.prevent="onJoin">
							<input v-model="joinCode" placeholder="Enter Code" maxlength="6" class="flex-1 px-4 py-3 rounded-md border border-slate-300 focus:outline-none focus:ring-2 focus:ring-orange-400" />
							<button type="submit" class="px-5 py-3 rounded-md bg-orange-500 text-white hover:bg-orange-600">Join</button>
						</form>
						<p v-if="authStatus==='signed-in'" class="mt-3 text-sm text-slate-500">Signed in anonymously for secure participation.</p>
					</div>
				</div>
			</div>
		</div>
		<section class="border-t border-slate-200 bg-white py-16">
			<div class="mx-auto max-w-7xl px-6 grid md:grid-cols-3 gap-8">
				<div>
					<i class="fa-solid fa-wand-magic-sparkles text-orange-500 text-2xl"></i>
					<h3 class="mt-3 font-semibold text-slate-900">AI-Powered Generation</h3>
					<p class="mt-2 text-slate-600">Generate targeted items by standard and misconception in seconds.</p>
				</div>
				<div>
					<i class="fa-solid fa-chart-line text-orange-500 text-2xl"></i>
					<h3 class="mt-3 font-semibold text-slate-900">Psychometric Analytics</h3>
					<p class="mt-2 text-slate-600">Difficulty, discrimination, and actionable insights—beautifully presented.</p>
				</div>
				<div>
					<i class="fa-solid fa-shield text-orange-500 text-2xl"></i>
					<h3 class="mt-3 font-semibold text-slate-900">Seamless & Secure</h3>
					<p class="mt-2 text-slate-600">Anonymous student identity with privacy by design.</p>
				</div>
			</div>
		</section>
	</section>
</template>

<script setup lang="ts">
const joinCode = ref('')
const router = useRouter()
const authStatus = useState<'idle'|'signed-in'>('authStatus', ()=>'idle')

onMounted(async()=>{
	const { signInAnonymouslyIfNeeded } = useStudentAuth()
	await signInAnonymouslyIfNeeded()
	authStatus.value = 'signed-in'
})

function onJoin(){
	if(joinCode.value.trim().length===6){
		router.push({ path: '/join', query: { code: joinCode.value.trim().toUpperCase() } })
	}
}
</script>

