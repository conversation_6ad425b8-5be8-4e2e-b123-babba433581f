<template>
  <div class="min-h-screen flex items-center justify-center">
    <div class="w-full max-w-lg">
      <!-- Main Join Card -->
      <div class="bg-white rounded-3xl shadow-2xl p-12 text-center">
        <!-- Logo -->
        <div class="inline-flex items-center space-x-4 mb-8">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center">
            <font-awesome-icon icon="check" class="text-white text-2xl" />
          </div>
          <h1 class="text-4xl font-bold text-secondary-800">CHECKPOINT</h1>
        </div>
        
        <p class="text-lg text-secondary-600 mb-12">Enter your details to join an assessment</p>

        <!-- Join Form -->
        <form @submit.prevent="handleJoin" class="space-y-6">
          <div>
            <label for="nickname" class="block text-left text-sm font-medium text-secondary-700 mb-3">
              Your Name
            </label>
            <input
              id="nickname"
              v-model="nickname"
              type="text"
              required
              maxlength="50"
              class="w-full px-6 py-4 text-lg border-2 border-secondary-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              placeholder="Enter your name or nickname"
            />
          </div>

          <div>
            <label for="joinCode" class="block text-left text-sm font-medium text-secondary-700 mb-3">
              Join Code
            </label>
            <input
              id="joinCode"
              v-model="joinCode"
              type="text"
              required
              maxlength="6"
              pattern="[A-Z0-9]{6}"
              class="w-full px-6 py-4 text-lg text-center font-mono border-2 border-secondary-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors uppercase tracking-widest"
              placeholder="ABC123"
              @input="formatJoinCode"
            />
            <p class="text-xs text-secondary-500 mt-2">Enter the 6-character code from your teacher</p>
          </div>

          <button
            type="submit"
            :disabled="isLoading || !isFormValid"
            class="w-full bg-gradient-to-r from-primary-600 to-accent-500 text-white py-4 px-8 rounded-xl text-lg font-semibold hover:from-primary-700 hover:to-accent-600 focus:outline-none focus:ring-4 focus:ring-primary-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
          >
            <font-awesome-icon v-if="isLoading" icon="spinner" spin class="mr-3" />
            {{ isLoading ? 'Joining...' : 'Join Assessment' }}
          </button>
        </form>

        <!-- Error Message -->
        <div v-if="error" class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-800 text-sm">{{ error }}</p>
        </div>

        <!-- Teacher Login Link -->
        <div class="mt-12 pt-8 border-t border-secondary-200">
          <p class="text-sm text-secondary-500">
            Are you a teacher?
            <NuxtLink to="/login" class="text-primary-600 hover:text-primary-700 font-medium">
              Sign in here
            </NuxtLink>
          </p>
        </div>
      </div>

      <!-- Help Section -->
      <div class="mt-8 text-center">
        <div class="bg-primary-50 rounded-2xl p-6">
          <h3 class="font-semibold text-secondary-800 mb-2">How to Join</h3>
          <div class="text-sm text-secondary-600 space-y-2">
            <p>1. Enter your name or nickname</p>
            <p>2. Type the 6-character join code from your teacher</p>
            <p>3. Click "Join Assessment" to start</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const nickname = ref('');
const joinCode = ref('');
const isLoading = ref(false);
const error = ref('');

const isFormValid = computed(() => {
  return nickname.value.trim() && joinCode.value.length === 6;
});

const formatJoinCode = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const value = input.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
  joinCode.value = value.slice(0, 6);
};

const handleJoin = async () => {
  if (!isFormValid.value) return;
  
  error.value = '';
  isLoading.value = true;

  try {
    // First validate the join code
    const sessionData = await $fetch(`/api/sessions/join/${joinCode.value}`);
    
    if (sessionData) {
      // Store participant info in session storage
      sessionStorage.setItem('participantNickname', nickname.value.trim());
      sessionStorage.setItem('joinCode', joinCode.value);
      
      // Navigate to the session page
      await navigateTo(`/session/${sessionData.session_id}/play`);
    }
  } catch (err: any) {
    if (err.status === 404) {
      error.value = 'Invalid join code. Please check with your teacher.';
    } else if (err.status === 400) {
      error.value = 'This session has ended or is no longer available.';
    } else {
      error.value = 'Unable to join session. Please try again.';
    }
  } finally {
    isLoading.value = false;
  }
};

// Set page title
useHead({
  title: 'Join Assessment - CHECKPOINT'
});
</script>