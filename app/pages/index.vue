<template>
  <div>
    <!-- Hero Section -->
    <section class="py-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <!-- Left Content -->
          <div class="space-y-8">
            <div class="space-y-6">
              <h1 class="text-4xl lg:text-5xl font-bold text-slate-800 leading-tight">
                Advanced Educational
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600">
                  Assessment Platform
                </span>
              </h1>
              <p class="text-lg text-slate-600 leading-relaxed">
                CHECKPOINT empowers educators to create AI-powered diagnostic assessments that identify student misconceptions and provide actionable insights for better learning outcomes.
              </p>
            </div>

            <div class="flex flex-col sm:flex-row gap-4">
              <NuxtLink
                to="/register"
                class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-xl transition-all duration-300 text-center"
              >
                Start Creating Assessments
              </NuxtLink>
              <NuxtLink
                to="/join"
                class="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-50 transition-all duration-300 text-center"
              >
                Join Assessment
              </NuxtLink>
            </div>

            <!-- Quick Access for Students -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-slate-200 shadow-sm">
              <h3 class="text-lg font-semibold text-slate-800 mb-4">Quick Student Access</h3>
              <div class="flex gap-3">
                <input
                  v-model="quickJoinCode"
                  type="text"
                  maxlength="6"
                  placeholder="Enter Code"
                  class="flex-1 px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-center font-mono text-lg uppercase"
                  @input="formatQuickCode"
                />
                <button
                  @click="quickJoin"
                  :disabled="quickJoinCode.length !== 6"
                  class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Join
                </button>
              </div>
            </div>
          </div>

          <!-- Right Content - Feature Preview -->
          <div class="relative">
            <div class="bg-white rounded-3xl shadow-2xl p-8 border border-slate-200">
              <div class="space-y-6">
                <div class="flex items-center space-x-3 mb-6">
                  <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <FontAwesomeIcon icon="wand-magic-sparkles" class="text-blue-600" />
                  </div>
                  <h3 class="text-lg font-semibold text-slate-800">AI-Powered Question Generation</h3>
                </div>
                <div class="bg-slate-50 rounded-lg p-4">
                  <p class="text-sm text-slate-700 mb-3">Generate diagnostic questions with AI:</p>
                  <div class="bg-white rounded border p-3 text-sm">
                    "Create a multiple choice question about photosynthesis for 5th graders, targeting the misconception that plants get food from soil."
                  </div>
                </div>
                <div class="flex items-center justify-between text-sm text-slate-600">
                  <span>✓ Misconception-focused</span>
                  <span>✓ Grade-level appropriate</span>
                  <span>✓ Instant generation</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-16">
          <h2 class="text-4xl font-bold text-slate-800 mb-4">
            Why Choose CHECKPOINT?
          </h2>
          <p class="text-xl text-slate-600 max-w-3xl mx-auto">
            Our platform combines cutting-edge AI with proven pedagogical principles to deliver unparalleled assessment capabilities.
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- Feature 1 -->
          <div class="group p-8 rounded-2xl border border-slate-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6">
              <FontAwesomeIcon icon="wand-magic-sparkles" class="text-white text-xl" />
            </div>
            <h3 class="text-xl font-semibold text-slate-800 mb-4">AI Question Generation</h3>
            <p class="text-slate-600">
              Generate diagnostic questions instantly using natural language prompts. Target specific misconceptions with precision.
            </p>
          </div>

          <!-- Feature 2 -->
          <div class="group p-8 rounded-2xl border border-slate-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6">
              <FontAwesomeIcon icon="users" class="text-white text-xl" />
            </div>
            <h3 class="text-xl font-semibold text-slate-800 mb-4">Real-time Sessions</h3>
            <p class="text-slate-600">
              Host live assessment sessions with instant participation tracking. Students join with simple 6-character codes.
            </p>
          </div>

          <!-- Feature 3 -->
          <div class="group p-8 rounded-2xl border border-slate-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6">
              <FontAwesomeIcon icon="chart-bar" class="text-white text-xl" />
            </div>
            <h3 class="text-xl font-semibold text-slate-800 mb-4">Deep Analytics</h3>
            <p class="text-slate-600">
              Get comprehensive reports with misconception analysis, performance insights, and actionable recommendations.
            </p>
          </div>

          <!-- Feature 4 -->
          <div class="group p-8 rounded-2xl border border-slate-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6">
              <FontAwesomeIcon icon="play" class="text-white text-xl" />
            </div>
            <h3 class="text-xl font-semibold text-slate-800 mb-4">Easy Deployment</h3>
            <p class="text-slate-600">
              Launch assessments instantly. Share join codes or direct links. No complex setup required for students.
            </p>
          </div>

          <!-- Feature 5 -->
          <div class="group p-8 rounded-2xl border border-slate-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6">
              <FontAwesomeIcon icon="book" class="text-white text-xl" />
            </div>
            <h3 class="text-xl font-semibold text-slate-800 mb-4">Pedagogical Focus</h3>
            <p class="text-slate-600">
              Built specifically for educational assessment. Every feature is designed with teaching and learning in mind.
            </p>
          </div>

          <!-- Feature 6 -->
          <div class="group p-8 rounded-2xl border border-slate-200 hover:border-blue-300 hover:shadow-lg transition-all duration-300">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6">
              <FontAwesomeIcon icon="cog" class="text-white text-xl" />
            </div>
            <h3 class="text-xl font-semibold text-slate-800 mb-4">Enterprise Ready</h3>
            <p class="text-slate-600">
              Scalable infrastructure, robust security, and comprehensive logging for institutional deployment.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-16 bg-gradient-to-r from-blue-600 to-indigo-600">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-4xl font-bold text-white mb-6">
          Ready to Transform Your Assessments?
        </h2>
        <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
          Join educators worldwide who are using CHECKPOINT to create more effective, insightful assessments.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <NuxtLink
            to="/register"
            class="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-blue-50 transition-all duration-300"
          >
            Get Started Free
          </NuxtLink>
          <NuxtLink
            to="/login"
            class="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white hover:text-blue-600 transition-all duration-300"
          >
            Sign In
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-slate-900 text-slate-300">
      <div class="container mx-auto px-4 py-12">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon="check" class="text-white" />
              </div>
              <span class="text-xl font-bold text-white">CHECKPOINT</span>
            </div>
            <p class="text-slate-400">
              Advanced educational assessment platform powered by AI for better learning outcomes.
            </p>
          </div>

          <div>
            <h3 class="text-white font-semibold mb-4">For Educators</h3>
            <ul class="space-y-2">
              <li><NuxtLink to="/register" class="hover:text-blue-400 transition-colors">Create Account</NuxtLink></li>
              <li><NuxtLink to="/login" class="hover:text-blue-400 transition-colors">Teacher Login</NuxtLink></li>
              <li><a href="#" class="hover:text-blue-400 transition-colors">Documentation</a></li>
            </ul>
          </div>

          <div>
            <h3 class="text-white font-semibold mb-4">For Students</h3>
            <ul class="space-y-2">
              <li><NuxtLink to="/join" class="hover:text-blue-400 transition-colors">Join Assessment</NuxtLink></li>
              <li><a href="#" class="hover:text-blue-400 transition-colors">Help & Support</a></li>
            </ul>
          </div>
        </div>

        <div class="border-t border-slate-800 mt-8 pt-8 text-center">
          <p>&copy; 2025 CHECKPOINT. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
const quickJoinCode = ref('');

const formatQuickCode = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const value = input.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
  quickJoinCode.value = value.slice(0, 6);
};

const quickJoin = () => {
  if (quickJoinCode.value.length === 6) {
    navigateTo(`/join?code=${quickJoinCode.value}`);
  }
};

useHead({
  title: 'CHECKPOINT - Advanced Educational Assessment Platform',
  meta: [
    {
      name: 'description',
      content: 'AI-powered educational assessment platform for creating diagnostic tests and identifying student misconceptions.'
    }
  ]
});
</script>