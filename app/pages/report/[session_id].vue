<template>
  <div class="max-w-7xl mx-auto">
    <!-- Header Actions -->
    <div class="flex items-center justify-end space-x-3 mb-6">
      <button
        @click="exportReport"
        class="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 transition-colors flex items-center space-x-2"
      >
        <font-awesome-icon icon="download" />
        <span>Export</span>
      </button>
      <NuxtLink
        to="/dashboard"
        class="bg-secondary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-secondary-700 transition-colors"
      >
        Dashboard
      </NuxtLink>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center h-64">
      <div class="text-center">
        <font-awesome-icon icon="spinner" spin class="text-primary-500 text-3xl mb-4" />
        <p class="text-secondary-600">Loading report...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex items-center justify-center h-64">
      <div class="text-center">
        <font-awesome-icon icon="times" class="text-red-500 text-3xl mb-4" />
        <h3 class="text-xl font-semibold text-secondary-800 mb-2">Report Not Available</h3>
        <p class="text-secondary-600 mb-6">{{ error }}</p>
        <NuxtLink
          to="/dashboard"
          class="bg-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Return to Dashboard
        </NuxtLink>
      </div>
    </div>

    <!-- Main Report -->
    <div v-else class="space-y-8">
      <!-- Report Header -->
      <div class="bg-gradient-to-r from-primary-600 to-accent-500 rounded-2xl p-8 text-white">
        <div class="flex items-start justify-between">
          <div>
            <h1 class="text-3xl font-bold mb-2">{{ report.assessment_title }}</h1>
            <p class="text-primary-100 mb-4">{{ report.description || 'Assessment Report' }}</p>
            <div class="flex items-center space-x-6 text-sm">
              <div class="flex items-center space-x-2">
                <font-awesome-icon icon="users" />
                <span>{{ report.total_participants }} participants</span>
              </div>
              <div class="flex items-center space-x-2">
                <font-awesome-icon icon="book" />
                <span>{{ report.total_questions }} questions</span>
              </div>
              <div class="flex items-center space-x-2">
                <font-awesome-icon icon="clock" />
                <span>{{ formatDate(report.session_date) }}</span>
              </div>
            </div>
          </div>
          <div class="text-center">
            <div class="text-5xl font-bold mb-2">{{ Math.round(report.average_score) }}%</div>
            <p class="text-primary-100">Average Score</p>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <div class="bg-white rounded-xl shadow-lg">
        <div class="border-b border-secondary-200">
          <nav class="flex space-x-8 p-6">
            <button
              @click="activeTab = 'overview'"
              :class="[
                'py-2 px-1 border-b-2 font-medium text-sm',
                activeTab === 'overview'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
              ]"
            >
              Overview
            </button>
            <button
              @click="activeTab = 'questions'"
              :class="[
                'py-2 px-1 border-b-2 font-medium text-sm',
                activeTab === 'questions'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
              ]"
            >
              Question Analysis
            </button>
            <button
              @click="activeTab = 'participants'"
              :class="[
                'py-2 px-1 border-b-2 font-medium text-sm',
                activeTab === 'participants'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-secondary-500 hover:text-secondary-700 hover:border-secondary-300'
              ]"
            >
              Participant Results
            </button>
          </nav>
        </div>

        <!-- Overview Tab -->
        <div v-if="activeTab === 'overview'" class="p-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Key Metrics -->
            <div>
              <h3 class="text-lg font-semibold text-secondary-800 mb-6">Key Metrics</h3>
              <div class="grid grid-cols-2 gap-4">
                <div class="bg-primary-50 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-primary-600">{{ report.completion_rate }}%</div>
                  <div class="text-sm text-primary-700">Completion Rate</div>
                </div>
                <div class="bg-accent-50 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-accent-600">{{ formatTime(report.average_time) }}</div>
                  <div class="text-sm text-accent-700">Avg. Time</div>
                </div>
                <div class="bg-green-50 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-green-600">{{ report.highest_score }}%</div>
                  <div class="text-sm text-green-700">Highest Score</div>
                </div>
                <div class="bg-red-50 rounded-lg p-4 text-center">
                  <div class="text-2xl font-bold text-red-600">{{ report.lowest_score }}%</div>
                  <div class="text-sm text-red-700">Lowest Score</div>
                </div>
              </div>
            </div>

            <!-- Score Distribution Chart -->
            <div>
              <h3 class="text-lg font-semibold text-secondary-800 mb-6">Score Distribution</h3>
              <div class="space-y-3">
                <div v-for="range in scoreRanges" :key="range.label" class="flex items-center">
                  <div class="w-20 text-sm text-secondary-600">{{ range.label }}</div>
                  <div class="flex-1 bg-secondary-200 rounded-full h-6 mx-3">
                    <div 
                      class="bg-primary-500 h-6 rounded-full flex items-center justify-end pr-2"
                      :style="{ width: `${range.percentage}%` }"
                    >
                      <span v-if="range.percentage > 15" class="text-white text-xs font-medium">
                        {{ range.count }}
                      </span>
                    </div>
                  </div>
                  <div class="w-12 text-sm text-secondary-600">{{ range.percentage }}%</div>
                </div>
              </div>
            </div>
          </div>

          <!-- Diagnostic Insights -->
          <div class="mt-8">
            <h3 class="text-lg font-semibold text-secondary-800 mb-6">Diagnostic Insights</h3>
            <div v-if="report.insights?.length" class="space-y-4">
              <div
                v-for="insight in report.insights"
                :key="insight.id"
                class="bg-amber-50 border border-amber-200 rounded-lg p-4"
              >
                <div class="flex items-start space-x-3">
                  <font-awesome-icon icon="wand-magic-sparkles" class="text-amber-600 mt-1" />
                  <div>
                    <h4 class="font-medium text-amber-800 mb-1">{{ insight.title }}</h4>
                    <p class="text-amber-700 text-sm">{{ insight.description }}</p>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8 text-secondary-500">
              <font-awesome-icon icon="chart-bar" class="text-3xl mb-3 opacity-50" />
              <p>No specific insights available for this assessment.</p>
            </div>
          </div>
        </div>

        <!-- Questions Tab -->
        <div v-if="activeTab === 'questions'" class="p-6">
          <div class="space-y-6">
            <div
              v-for="(question, index) in report.question_analysis"
              :key="index"
              class="border border-secondary-200 rounded-lg p-6"
            >
              <div class="flex items-start justify-between mb-4">
                <div class="flex-1">
                  <h4 class="font-semibold text-secondary-800 mb-2">
                    Question {{ index + 1 }}: {{ question.question_text }}
                  </h4>
                  <div class="flex items-center space-x-4 text-sm text-secondary-600">
                    <span>{{ question.total_responses }} responses</span>
                    <span>{{ Math.round(question.correct_percentage) }}% correct</span>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-2xl font-bold" :class="[
                    question.correct_percentage >= 80 ? 'text-green-600' :
                    question.correct_percentage >= 60 ? 'text-yellow-600' : 'text-red-600'
                  ]">
                    {{ Math.round(question.correct_percentage) }}%
                  </div>
                  <div class="text-xs text-secondary-500">Correct</div>
                </div>
              </div>

              <!-- Option Analysis -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div
                  v-for="(option, optionIndex) in question.options"
                  :key="optionIndex"
                  class="flex items-center space-x-3"
                >
                  <div :class="[
                    'w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm',
                    option.is_correct 
                      ? 'border-green-500 bg-green-50 text-green-700' 
                      : 'border-secondary-300 text-secondary-600'
                  ]">
                    {{ String.fromCharCode(65 + optionIndex) }}
                    <font-awesome-icon v-if="option.is_correct" icon="check" class="ml-1 text-xs" />
                  </div>
                  <div class="flex-1">
                    <div class="text-sm text-secondary-800 mb-1">{{ option.text }}</div>
                    <div class="flex items-center space-x-2">
                      <div class="flex-1 bg-secondary-200 rounded-full h-2">
                        <div 
                          :class="[
                            'h-2 rounded-full',
                            option.is_correct ? 'bg-green-500' : 'bg-secondary-400'
                          ]"
                          :style="{ width: `${option.percentage}%` }"
                        ></div>
                      </div>
                      <span class="text-xs text-secondary-600 w-12">{{ option.count }} ({{ Math.round(option.percentage) }}%)</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Common Misconceptions -->
              <div v-if="question.misconceptions?.length" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <h5 class="font-medium text-red-800 mb-2">Common Misconceptions:</h5>
                <ul class="list-disc list-inside text-sm text-red-700 space-y-1">
                  <li v-for="misconception in question.misconceptions" :key="misconception">
                    {{ misconception }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- Participants Tab -->
        <div v-if="activeTab === 'participants'" class="p-6">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-secondary-200">
              <thead class="bg-secondary-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Participant
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Score
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Correct Answers
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Time Taken
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-secondary-500 uppercase tracking-wider">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-secondary-200">
                <tr
                  v-for="participant in report.participant_results"
                  :key="participant.id"
                  class="hover:bg-secondary-50"
                >
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center mr-3">
                        <span class="text-white font-semibold text-sm">
                          {{ participant.nickname.charAt(0).toUpperCase() }}
                        </span>
                      </div>
                      <span class="text-sm font-medium text-secondary-900">{{ participant.nickname }}</span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <span class="text-sm font-semibold" :class="[
                        participant.score >= 80 ? 'text-green-600' :
                        participant.score >= 60 ? 'text-yellow-600' : 'text-red-600'
                      ]">
                        {{ Math.round(participant.score) }}%
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                    {{ participant.correct_answers }} / {{ report.total_questions }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-secondary-900">
                    {{ formatTime(participant.time_taken) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="[
                      'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                      participant.completed 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    ]">
                      {{ participant.completed ? 'Completed' : 'Incomplete' }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'teacher',
  middleware: 'auth'
});

const route = useRoute();
const sessionId = route.params.session_id as string;

// State
const report = ref<any>({});
const isLoading = ref(true);
const error = ref('');
const activeTab = ref('overview');

// Computed
const scoreRanges = computed(() => {
  if (!report.value.score_distribution) return [];
  
  const total = report.value.total_participants;
  return [
    { label: '90-100%', count: report.value.score_distribution['90-100'] || 0, percentage: Math.round(((report.value.score_distribution['90-100'] || 0) / total) * 100) },
    { label: '80-89%', count: report.value.score_distribution['80-89'] || 0, percentage: Math.round(((report.value.score_distribution['80-89'] || 0) / total) * 100) },
    { label: '70-79%', count: report.value.score_distribution['70-79'] || 0, percentage: Math.round(((report.value.score_distribution['70-79'] || 0) / total) * 100) },
    { label: '60-69%', count: report.value.score_distribution['60-69'] || 0, percentage: Math.round(((report.value.score_distribution['60-69'] || 0) / total) * 100) },
    { label: '50-59%', count: report.value.score_distribution['50-59'] || 0, percentage: Math.round(((report.value.score_distribution['50-59'] || 0) / total) * 100) },
    { label: '0-49%', count: report.value.score_distribution['0-49'] || 0, percentage: Math.round(((report.value.score_distribution['0-49'] || 0) / total) * 100) },
  ];
});

// Load report data
onMounted(async () => {
  try {
    const data = await $fetch(`/api/reports/${sessionId}`);
    report.value = data;
  } catch (err: any) {
    error.value = err.status === 404 
      ? 'Report not found or session has not been completed yet.'
      : 'Failed to load report data.';
  } finally {
    isLoading.value = false;
  }
});

const exportReport = () => {
  // Generate and download report as JSON or CSV
  const dataStr = JSON.stringify(report.value, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `checkpoint-report-${sessionId}-${new Date().toISOString().split('T')[0]}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatTime = (seconds: number) => {
  if (!seconds) return '0s';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
};

useHead({
  title: computed(() => `Report: ${report.value.assessment_title || 'Session'} - CHECKPOINT`)
});
</script>