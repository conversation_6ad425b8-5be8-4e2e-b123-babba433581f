<template>
  <div class="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 flex items-center justify-center py-8">
    <!-- Navigation -->
    <div class="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-sm border-b border-secondary-200">
      <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <NuxtLink to="/" class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <FontAwesomeIcon icon="check" class="text-white" />
            </div>
            <span class="text-xl font-bold text-secondary-800">CHECKPOINT</span>
          </NuxtLink>
          <NuxtLink
            to="/"
            class="text-secondary-600 hover:text-secondary-800 font-medium transition-colors"
          >
            ← Back to Home
          </NuxtLink>
        </div>
      </div>
    </div>

    <div class="w-full max-w-lg px-4 pt-20">
      <!-- Main Join Card -->
      <div class="bg-white rounded-3xl shadow-2xl p-12 text-center border border-secondary-200">
        <!-- Logo -->
        <div class="inline-flex items-center space-x-4 mb-8">
          <div class="w-16 h-16 bg-gradient-to-br from-primary-500 to-accent-500 rounded-2xl flex items-center justify-center">
            <FontAwesomeIcon icon="users" class="text-white text-2xl" />
          </div>
          <div class="text-left">
            <h1 class="text-3xl font-bold text-secondary-800">Join Assessment</h1>
            <p class="text-secondary-600">Enter your details to participate</p>
          </div>
        </div>

        <!-- Join Form -->
        <form @submit.prevent="handleJoin" class="space-y-6">
          <div>
            <label for="nickname" class="block text-left text-sm font-medium text-secondary-700 mb-3">
              Your Name
            </label>
            <input
              id="nickname"
              v-model="nickname"
              type="text"
              required
              maxlength="50"
              class="w-full px-6 py-4 text-lg border-2 border-secondary-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors"
              placeholder="Enter your name or nickname"
            />
          </div>

          <div>
            <label for="joinCode" class="block text-left text-sm font-medium text-secondary-700 mb-3">
              Assessment Code
            </label>
            <input
              id="joinCode"
              v-model="joinCode"
              type="text"
              required
              maxlength="6"
              pattern="[A-Z0-9]{6}"
              class="w-full px-6 py-4 text-lg text-center font-mono border-2 border-secondary-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors uppercase tracking-widest"
              placeholder="ABC123"
              @input="formatJoinCode"
            />
            <p class="text-xs text-secondary-500 mt-2">Enter the 6-character code from your teacher</p>
          </div>

          <button
            type="submit"
            :disabled="isLoading || !isFormValid"
            class="w-full bg-gradient-to-r from-primary-600 to-accent-500 text-white py-4 px-8 rounded-xl text-lg font-semibold hover:from-primary-700 hover:to-accent-600 focus:outline-none focus:ring-4 focus:ring-primary-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
          >
            <FontAwesomeIcon v-if="isLoading" icon="spinner" spin class="mr-3" />
            {{ isLoading ? 'Joining...' : 'Join Assessment' }}
          </button>
        </form>

        <!-- Success Message -->
        <div v-if="successMessage" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
          <p class="text-green-800 text-sm">{{ successMessage }}</p>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-800 text-sm">{{ error }}</p>
        </div>

        <!-- Help Section -->
        <div class="mt-8 pt-6 border-t border-secondary-200">
          <h3 class="font-semibold text-secondary-800 mb-3">Need Help?</h3>
          <div class="text-sm text-secondary-600 space-y-2">
            <p>• Ask your teacher for the 6-character assessment code</p>
            <p>• Make sure you enter your real name for identification</p>
            <p>• If the code doesn't work, check with your teacher</p>
          </div>
        </div>

        <!-- Teacher Login Link -->
        <div class="mt-8 pt-6 border-t border-secondary-200">
          <p class="text-sm text-secondary-500">
            Are you a teacher?
            <NuxtLink to="/login" class="text-primary-600 hover:text-primary-700 font-medium">
              Sign in here
            </NuxtLink>
          </p>
        </div>
      </div>

      <!-- Features Preview -->
      <div class="mt-8 grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 text-center border border-secondary-200">
          <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-2">
            <FontAwesomeIcon icon="play" class="text-primary-600" />
          </div>
          <p class="text-sm font-medium text-secondary-800">Interactive</p>
          <p class="text-xs text-secondary-600">Real-time participation</p>
        </div>

        <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 text-center border border-secondary-200">
          <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-2">
            <FontAwesomeIcon icon="check" class="text-primary-600" />
          </div>
          <p class="text-sm font-medium text-secondary-800">Simple</p>
          <p class="text-xs text-secondary-600">No account needed</p>
        </div>

        <div class="bg-white/70 backdrop-blur-sm rounded-2xl p-4 text-center border border-secondary-200">
          <div class="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-2">
            <FontAwesomeIcon icon="chart-bar" class="text-primary-600" />
          </div>
          <p class="text-sm font-medium text-secondary-800">Insightful</p>
          <p class="text-xs text-secondary-600">Instant feedback</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const nickname = ref('');
const joinCode = ref((route.query.code as string) || '');
const isLoading = ref(false);
const error = ref('');
const successMessage = ref('');

const isFormValid = computed(() => {
  return nickname.value.trim() && joinCode.value.length === 6;
});

const formatJoinCode = (event: Event) => {
  const input = event.target as HTMLInputElement;
  const value = input.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
  joinCode.value = value.slice(0, 6);
};

const handleJoin = async () => {
  if (!isFormValid.value) return;
  
  error.value = '';
  successMessage.value = '';
  isLoading.value = true;

  try {
    // First validate the join code
    const sessionData = await $fetch(`/api/sessions/join/${joinCode.value}`);
    
    if (sessionData) {
      successMessage.value = `Found assessment: ${sessionData.assessment_title}`;
      
      // Short delay to show success message
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Store participant info in session storage
      sessionStorage.setItem('participantNickname', nickname.value.trim());
      sessionStorage.setItem('joinCode', joinCode.value);
      
      // Navigate to the session page
      await navigateTo(`/session/${sessionData.session_id}/play`);
    }
  } catch (err: any) {
    if (err.status === 404) {
      error.value = 'Invalid assessment code. Please check with your teacher and try again.';
    } else if (err.status === 400) {
      error.value = 'This assessment has ended or is no longer available.';
    } else {
      error.value = 'Unable to join assessment. Please check your connection and try again.';
    }
  } finally {
    isLoading.value = false;
  }
};

// Auto-format if code is provided in URL
onMounted(() => {
  if (joinCode.value) {
    joinCode.value = joinCode.value.toUpperCase().slice(0, 6);
  }
});

// Set page title
useHead({
  title: 'Join Assessment - CHECKPOINT',
  meta: [
    {
      name: 'description',
      content: 'Join a CHECKPOINT assessment session with your teacher-provided code.'
    }
  ]
});
</script>