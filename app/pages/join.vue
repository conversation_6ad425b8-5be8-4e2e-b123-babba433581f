<template>
	<section class="max-w-xl mx-auto py-16">
		<div class="bg-white border border-slate-200 shadow-sm rounded-xl p-6">
			<h1 class="text-xl font-semibold text-slate-900">Join Session</h1>
			<form class="mt-6 space-y-4" @submit.prevent="submit">
				<label class="block">
					<span class="text-sm text-slate-600">6-digit Code</span>
					<input v-model="code" maxlength="6" class="mt-1 w-full px-4 py-3 rounded-md border border-slate-300 focus:outline-none focus:ring-2 focus:ring-orange-400" />
				</label>
				<label class="block">
					<span class="text-sm text-slate-600">Nickname</span>
					<input v-model="nickname" maxlength="20" placeholder="e.g., Alex" class="mt-1 w-full px-4 py-3 rounded-md border border-slate-300 focus:outline-none focus:ring-2 focus:ring-orange-400" />
				</label>
				<button type="submit" class="w-full px-5 py-3 rounded-md bg-orange-500 text-white hover:bg-orange-600">Let's Go!</button>
			</form>
		</div>
	</section>
</template>

<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const code = ref<string>((route.query.code as string)||'')
const nickname = ref('')

onMounted(async()=>{
	const { signInAnonymouslyIfNeeded, getIdToken } = useStudentAuth()
	await signInAnonymouslyIfNeeded()
	if(!code.value){
		const c = localStorage.getItem('cp_last_join_code')
		if(c) code.value = c
	}
})

async function submit(){
	const cleanCode = code.value.trim().toUpperCase()
	if(cleanCode.length!==6||!nickname.value.trim()) return
	localStorage.setItem('cp_last_join_code', cleanCode)
	const { getIdToken } = useStudentAuth()
	const idToken = await getIdToken()
	const res = await $fetch('/api/session/join', { method: 'POST', body: { code: cleanCode, nickname: nickname.value.trim(), idToken } })
	if(res && (res as any).sessionId){
		router.push(`/session/${(res as any).sessionId}`)
	}
}
</script>

