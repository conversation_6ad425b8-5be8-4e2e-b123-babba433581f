<template>
  <div class="space-y-8 max-w-none">
    <!-- Welcome Section -->
    <div class="bg-gradient-to-r from-primary-600 to-accent-500 rounded-2xl p-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Welcome back, {{ user?.displayName || 'Teacher' }}!</h1>
          <p class="text-primary-100 text-lg">Ready to create engaging diagnostic assessments?</p>
        </div>
        <div class="hidden md:block">
          <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
            <FontAwesomeIcon icon="chart-bar" class="text-3xl" />
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Create New Assessment -->
      <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-primary-500 hover:shadow-xl transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
            <FontAwesomeIcon icon="plus" class="text-primary-600 text-xl" />
          </div>
          <FontAwesomeIcon icon="arrow-right" class="text-secondary-400" />
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 mb-2">Create New Assessment</h3>
        <p class="text-secondary-600 text-sm mb-4">Design a diagnostic assessment with AI-powered question generation</p>
        <NuxtLink
          to="/assessment/new"
          class="inline-flex items-center text-primary-600 font-medium hover:text-primary-700 transition-colors"
        >
          Get Started
          <FontAwesomeIcon icon="arrow-right" class="ml-2" />
        </NuxtLink>
      </div>

      <!-- Quick Launch -->
      <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-accent-500 hover:shadow-xl transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center">
            <FontAwesomeIcon icon="play" class="text-accent-600 text-xl" />
          </div>
          <FontAwesomeIcon icon="arrow-right" class="text-secondary-400" />
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 mb-2">Quick Launch</h3>
        <p class="text-secondary-600 text-sm mb-4">Launch your most recent assessment instantly</p>
        <button
          @click="quickLaunch"
          :disabled="!recentAssessments.length"
          class="inline-flex items-center text-accent-600 font-medium hover:text-accent-700 transition-colors disabled:text-secondary-400 disabled:cursor-not-allowed"
        >
          Launch Now
          <FontAwesomeIcon icon="play" class="ml-2" />
        </button>
      </div>

      <!-- Analytics -->
      <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500 hover:shadow-xl transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
            <FontAwesomeIcon icon="chart-bar" class="text-green-600 text-xl" />
          </div>
          <FontAwesomeIcon icon="arrow-right" class="text-secondary-400" />
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 mb-2">Analytics Overview</h3>
        <p class="text-secondary-600 text-sm mb-4">View insights from your recent sessions</p>
        <div class="text-green-600 font-medium">
          {{ totalSessions }} sessions completed
        </div>
      </div>
    </div>

    <!-- Recent Assessments -->
    <div class="bg-white rounded-xl shadow-lg">
      <div class="p-6 border-b border-secondary-200">
        <div class="flex items-center justify-between">
          <h2 class="text-xl font-bold text-secondary-800">Recent Assessments</h2>
          <NuxtLink
            to="/assessment/new"
            class="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center space-x-2"
          >
            <FontAwesomeIcon icon="plus" />
            <span>New Assessment</span>
          </NuxtLink>
        </div>
      </div>

      <div v-if="isLoading" class="p-8 text-center">
        <FontAwesomeIcon icon="spinner" spin class="text-primary-500 text-2xl mb-4" />
        <p class="text-secondary-600">Loading your assessments...</p>
      </div>

      <div v-else-if="!recentAssessments.length" class="p-12 text-center">
        <div class="w-16 h-16 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <FontAwesomeIcon icon="book" class="text-secondary-400 text-2xl" />
        </div>
        <h3 class="text-lg font-semibold text-secondary-800 mb-2">No assessments yet</h3>
        <p class="text-secondary-600 mb-6">Create your first assessment to get started with CHECKPOINT</p>
        <NuxtLink
          to="/assessment/new"
          class="bg-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors inline-flex items-center space-x-2"
        >
          <FontAwesomeIcon icon="plus" />
          <span>Create First Assessment</span>
        </NuxtLink>
      </div>

      <div v-else class="divide-y divide-secondary-200">
        <div
          v-for="assessment in recentAssessments"
          :key="assessment.id"
          class="p-6 hover:bg-secondary-50 transition-colors"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <h3 class="text-lg font-semibold text-secondary-800">{{ assessment.title }}</h3>
                <span :class="[
                  'px-2 py-1 rounded-full text-xs font-medium',
                  assessment.status === 'published' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                ]">
                  {{ assessment.status === 'published' ? 'Published' : 'Draft' }}
                </span>
              </div>
              <p v-if="assessment.description" class="text-secondary-600 mb-2">{{ assessment.description }}</p>
              <div class="flex items-center space-x-4 text-sm text-secondary-500">
                <span>{{ assessment.item_count }} questions</span>
                <span>Updated {{ formatDate(assessment.updated_at) }}</span>
              </div>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <NuxtLink
                :to="`/assessment/${assessment.id}`"
                class="p-2 text-secondary-600 hover:text-primary-600 hover:bg-primary-50 rounded-lg transition-colors"
                title="Edit Assessment"
              >
                <FontAwesomeIcon icon="edit" />
              </NuxtLink>
              <button
                @click="launchAssessment(assessment.id)"
                :disabled="!assessment.item_count"
                class="p-2 text-secondary-600 hover:text-accent-600 hover:bg-accent-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Launch Assessment"
              >
                <FontAwesomeIcon icon="play" />
              </button>
              <button
                @click="deleteAssessment(assessment.id)"
                class="p-2 text-secondary-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="Delete Assessment"
              >
                <FontAwesomeIcon icon="trash" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div class="bg-white rounded-xl shadow-lg p-6 text-center">
        <div class="text-2xl font-bold text-secondary-800">{{ stats.totalAssessments }}</div>
        <div class="text-sm text-secondary-600">Total Assessments</div>
      </div>
      <div class="bg-white rounded-xl shadow-lg p-6 text-center">
        <div class="text-2xl font-bold text-primary-600">{{ stats.totalSessions }}</div>
        <div class="text-sm text-secondary-600">Sessions Run</div>
      </div>
      <div class="bg-white rounded-xl shadow-lg p-6 text-center">
        <div class="text-2xl font-bold text-accent-600">{{ stats.totalParticipants }}</div>
        <div class="text-sm text-secondary-600">Students Reached</div>
      </div>
      <div class="bg-white rounded-xl shadow-lg p-6 text-center">
        <div class="text-2xl font-bold text-green-600">{{ stats.avgScore }}%</div>
        <div class="text-sm text-secondary-600">Avg. Score</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'teacher',
  middleware: 'auth'
});

const { user } = useAuth();
const { authFetch } = useApi();

// State
interface AssessmentWithStats {
  id: string;
  title: string;
  description?: string;
  status: 'draft' | 'published';
  item_count: number;
  updated_at: string;
}

const recentAssessments = ref<AssessmentWithStats[]>([]);
const isLoading = ref(true);
const stats = ref({
  totalAssessments: 0,
  totalSessions: 0,
  totalParticipants: 0,
  avgScore: 0
});

// Computed
const totalSessions = computed(() => stats.value.totalSessions);

// Load data
onMounted(async () => {
  await loadDashboardData();
});

const loadDashboardData = async () => {
  isLoading.value = true;
  try {
    const [assessments, dashboardStats] = await Promise.all([
      authFetch('/api/assessments'),
      authFetch('/api/dashboard/stats')
    ]);
    
    recentAssessments.value = assessments || [];
    stats.value = {
      totalAssessments: assessments?.length || 0,
      totalSessions: dashboardStats?.totalSessions || 0,
      totalParticipants: dashboardStats?.totalParticipants || 0,
      avgScore: dashboardStats?.avgScore || 0
    };
  } catch (error) {
    console.error('Failed to load dashboard data:', error);
  } finally {
    isLoading.value = false;
  }
};

const quickLaunch = async () => {
  if (!recentAssessments.value.length) return;
  
  const mostRecent = recentAssessments.value[0];
  await launchAssessment(mostRecent.id);
};

const launchAssessment = async (assessmentId: string) => {
  try {
    const session = await authFetch('/api/sessions', {
      method: 'POST',
      body: { assessment_id: assessmentId }
    });
    
    if (session.session_id) {
      await navigateTo(`/session/${session.session_id}/host`);
    }
  } catch (error) {
    console.error('Failed to launch assessment:', error);
    // Show error message to user
  }
};

const deleteAssessment = async (assessmentId: string) => {
  if (!confirm('Are you sure you want to delete this assessment? This action cannot be undone.')) {
    return;
  }
  
  try {
    await authFetch(`/api/assessments/${assessmentId}`, {
      method: 'DELETE'
    });
    
    // Remove from local state
    recentAssessments.value = recentAssessments.value.filter(a => a.id !== assessmentId);
    stats.value.totalAssessments--;
  } catch (error) {
    console.error('Failed to delete assessment:', error);
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return 'yesterday';
  if (diffDays < 7) return `${diffDays} days ago`;
  return date.toLocaleDateString();
};

// Set page title
useHead({
  title: 'Dashboard - CHECKPOINT'
});
</script>