<template>
  <div class="space-y-8">
    <!-- Welcome Section -->
    <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white shadow-lg">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2">Welcome back, {{ user?.displayName || 'Teacher' }}!</h1>
          <p class="text-blue-100 text-lg">Ready to create engaging diagnostic assessments?</p>
        </div>
        <div class="hidden md:block">
          <div class="w-20 h-20 bg-white/20 rounded-2xl flex items-center justify-center">
            <FontAwesomeIcon icon="chart-bar" class="text-3xl" />
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Create New Assessment -->
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
            <FontAwesomeIcon icon="plus" class="text-blue-600 text-xl" />
          </div>
          <FontAwesomeIcon icon="arrow-right" class="text-slate-400" />
        </div>
        <h3 class="text-lg font-semibold text-slate-800 mb-2">Create New Assessment</h3>
        <p class="text-slate-600 text-sm mb-4">Design a diagnostic assessment with AI-powered question generation</p>
        <NuxtLink
          to="/assessment/new"
          class="inline-flex items-center text-blue-600 font-medium hover:text-blue-700 transition-colors"
        >
          Get Started
          <FontAwesomeIcon icon="arrow-right" class="ml-2 w-4 h-4" />
        </NuxtLink>
      </div>

      <!-- Quick Launch -->
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
            <FontAwesomeIcon icon="play" class="text-indigo-600 text-xl" />
          </div>
          <FontAwesomeIcon icon="arrow-right" class="text-slate-400" />
        </div>
        <h3 class="text-lg font-semibold text-slate-800 mb-2">Quick Launch</h3>
        <p class="text-slate-600 text-sm mb-4">Launch your most recent assessment instantly</p>
        <button
          @click="quickLaunch"
          :disabled="!recentAssessments.length"
          class="inline-flex items-center text-indigo-600 font-medium hover:text-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {{ recentAssessments.length ? 'Launch' : 'No assessments' }}
          <FontAwesomeIcon v-if="recentAssessments.length" icon="arrow-right" class="ml-2 w-4 h-4" />
        </button>
      </div>

      <!-- View Reports -->
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow">
        <div class="flex items-center justify-between mb-4">
          <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
            <FontAwesomeIcon icon="chart-bar" class="text-green-600 text-xl" />
          </div>
          <FontAwesomeIcon icon="arrow-right" class="text-slate-400" />
        </div>
        <h3 class="text-lg font-semibold text-slate-800 mb-2">View Reports</h3>
        <p class="text-slate-600 text-sm mb-4">Analyze student responses and diagnostic insights</p>
        <NuxtLink
          to="/reports"
          class="inline-flex items-center text-green-600 font-medium hover:text-green-700 transition-colors"
        >
          View All
          <FontAwesomeIcon icon="arrow-right" class="ml-2 w-4 h-4" />
        </NuxtLink>
      </div>
    </div>

    <!-- Recent Assessments -->
    <div class="bg-white rounded-2xl shadow-sm border border-slate-200">
      <div class="p-6 border-b border-slate-200">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-xl font-semibold text-slate-800">Recent Assessments</h2>
            <p class="text-slate-600 mt-1">Manage your diagnostic assessments</p>
          </div>
          <NuxtLink
            to="/assessment/new"
            class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center space-x-2 shadow-sm"
          >
            <FontAwesomeIcon icon="plus" />
            <span>New Assessment</span>
          </NuxtLink>
        </div>
      </div>

      <div v-if="isLoading" class="p-8 text-center">
        <FontAwesomeIcon icon="spinner" spin class="text-blue-500 text-2xl mb-4" />
        <p class="text-slate-600">Loading your assessments...</p>
      </div>

      <div v-else-if="!recentAssessments.length" class="p-12 text-center">
        <div class="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <FontAwesomeIcon icon="book" class="text-slate-400 text-2xl" />
        </div>
        <h3 class="text-lg font-semibold text-slate-800 mb-2">No assessments yet</h3>
        <p class="text-slate-600 mb-6">Create your first assessment to get started with CHECKPOINT</p>
        <NuxtLink
          to="/assessment/new"
          class="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors inline-flex items-center space-x-2 shadow-sm"
        >
          <FontAwesomeIcon icon="plus" />
          <span>Create First Assessment</span>
        </NuxtLink>
      </div>

      <div v-else class="divide-y divide-slate-200">
        <div
          v-for="assessment in recentAssessments"
          :key="assessment.id"
          class="p-6 hover:bg-slate-50 transition-colors"
        >
          <div class="flex items-center justify-between">
            <div class="flex-1">
              <div class="flex items-center space-x-3 mb-2">
                <h3 class="text-lg font-semibold text-slate-800">{{ assessment.title }}</h3>
                <span :class="[
                  'px-2 py-1 rounded-full text-xs font-medium',
                  assessment.status === 'published' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-yellow-100 text-yellow-800'
                ]">
                  {{ assessment.status === 'published' ? 'Published' : 'Draft' }}
                </span>
              </div>
              <p v-if="assessment.description" class="text-slate-600 mb-2">{{ assessment.description }}</p>
              <div class="flex items-center space-x-4 text-sm text-slate-500">
                <span class="flex items-center space-x-1">
                  <FontAwesomeIcon icon="book" class="w-4 h-4" />
                  <span>{{ assessment.item_count || 0 }} questions</span>
                </span>
                <span class="flex items-center space-x-1">
                  <FontAwesomeIcon icon="clock" class="w-4 h-4" />
                  <span>Updated {{ formatDate(assessment.updated_at) }}</span>
                </span>
              </div>
            </div>
            <div class="flex items-center space-x-2 ml-4">
              <NuxtLink
                :to="`/assessment/${assessment.id}`"
                class="p-2 text-slate-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                title="Edit Assessment"
              >
                <FontAwesomeIcon icon="edit" />
              </NuxtLink>
              <button
                @click="launchAssessment(assessment.id)"
                :disabled="!assessment.item_count"
                class="p-2 text-slate-600 hover:text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Launch Assessment"
              >
                <FontAwesomeIcon icon="play" />
              </button>
              <button
                @click="deleteAssessment(assessment.id)"
                class="p-2 text-slate-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                title="Delete Assessment"
              >
                <FontAwesomeIcon icon="trash" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Statistics -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 text-center">
        <div class="text-3xl font-bold text-slate-800 mb-1">{{ stats.totalAssessments }}</div>
        <div class="text-sm text-slate-600">Total Assessments</div>
      </div>
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 text-center">
        <div class="text-3xl font-bold text-blue-600 mb-1">{{ stats.totalSessions }}</div>
        <div class="text-sm text-slate-600">Sessions Run</div>
      </div>
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 text-center">
        <div class="text-3xl font-bold text-indigo-600 mb-1">{{ stats.totalParticipants }}</div>
        <div class="text-sm text-slate-600">Students Reached</div>
      </div>
      <div class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 text-center">
        <div class="text-3xl font-bold text-green-600 mb-1">{{ stats.avgScore }}%</div>
        <div class="text-sm text-slate-600">Avg. Score</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'teacher',
  middleware: 'auth'
});

const { user } = useAuth();
const { authFetch } = useApi();

// State
interface AssessmentWithStats {
  id: string;
  title: string;
  description?: string;
  status: 'draft' | 'published';
  item_count: number;
  updated_at: string;
}

const recentAssessments = ref<AssessmentWithStats[]>([]);
const isLoading = ref(true);
const stats = ref({
  totalAssessments: 0,
  totalSessions: 0,
  totalParticipants: 0,
  avgScore: 0
});

// Computed
const totalSessions = computed(() => stats.value.totalSessions);

// Load data
onMounted(async () => {
  await loadDashboardData();
});

const loadDashboardData = async () => {
  isLoading.value = true;
  try {
    const [assessments, dashboardStats] = await Promise.all([
      authFetch('/api/assessments'),
      authFetch('/api/dashboard/stats')
    ]);
    
    recentAssessments.value = assessments || [];
    stats.value = {
      totalAssessments: assessments?.length || 0,
      totalSessions: dashboardStats?.totalSessions || 0,
      totalParticipants: dashboardStats?.totalParticipants || 0,
      avgScore: dashboardStats?.avgScore || 0
    };
  } catch (error) {
    console.error('Failed to load dashboard data:', error);
  } finally {
    isLoading.value = false;
  }
};

const quickLaunch = async () => {
  if (!recentAssessments.value.length) return;
  
  const mostRecent = recentAssessments.value[0];
  await launchAssessment(mostRecent.id);
};

const launchAssessment = async (assessmentId: string) => {
  try {
    const session = await authFetch('/api/sessions', {
      method: 'POST',
      body: { assessment_id: assessmentId }
    });
    
    if (session.session_id) {
      await navigateTo(`/session/${session.session_id}/host`);
    }
  } catch (error) {
    console.error('Failed to launch assessment:', error);
    // Show error message to user
  }
};

const deleteAssessment = async (assessmentId: string) => {
  if (!confirm('Are you sure you want to delete this assessment? This action cannot be undone.')) {
    return;
  }
  
  try {
    await authFetch(`/api/assessments/${assessmentId}`, {
      method: 'DELETE'
    });
    
    // Remove from local state
    recentAssessments.value = recentAssessments.value.filter(a => a.id !== assessmentId);
    stats.value.totalAssessments--;
  } catch (error) {
    console.error('Failed to delete assessment:', error);
  }
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - date.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays === 1) return 'yesterday';
  if (diffDays < 7) return `${diffDays} days ago`;
  return date.toLocaleDateString();
};

// Set page title
useHead({
  title: 'Dashboard - CHECKPOINT'
});
</script>