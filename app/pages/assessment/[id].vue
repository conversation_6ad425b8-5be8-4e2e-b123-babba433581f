<template>
  <div class="max-w-7xl mx-auto">
    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center h-64">
      <div class="text-center">
        <font-awesome-icon icon="spinner" spin class="text-primary-500 text-3xl mb-4" />
        <p class="text-secondary-600">Loading assessment...</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex items-center justify-center h-64">
      <div class="text-center">
        <font-awesome-icon icon="times" class="text-red-500 text-3xl mb-4" />
        <h3 class="text-xl font-semibold text-secondary-800 mb-2">Assessment Not Found</h3>
        <p class="text-secondary-600 mb-6">{{ error }}</p>
        <NuxtLink
          to="/dashboard"
          class="bg-primary-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Return to Dashboard
        </NuxtLink>
      </div>
    </div>

    <!-- Main Editor -->
    <div v-else>
      <!-- Header Actions -->
      <div class="flex items-center justify-end space-x-3 mb-6">
        <div class="flex items-center space-x-3">
          <button
            @click="saveAssessment"
            :disabled="isSaving || !assessment.title?.trim()"
            class="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <font-awesome-icon v-if="isSaving" icon="spinner" spin />
            <font-awesome-icon v-else icon="check" />
            <span>{{ isSaving ? 'Saving...' : 'Save' }}</span>
          </button>
          
          <button
            @click="launchAssessment"
            :disabled="!canLaunch"
            class="bg-accent-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-accent-700 transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <font-awesome-icon icon="play" />
            <span>Launch</span>
          </button>

          <!-- Delete Button -->
          <button
            @click="deleteAssessment"
            class="bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors flex items-center space-x-2"
          >
            <font-awesome-icon icon="trash" />
            <span>Delete</span>
          </button>
        </div>
      </div>

      <div class="grid grid-cols-12 gap-6 h-[calc(100vh-200px)]">
        <!-- Left Sidebar - Items List -->
        <div class="col-span-4 bg-white rounded-xl shadow-lg flex flex-col">
          <div class="p-6 border-b border-secondary-200">
            <h3 class="text-lg font-semibold text-secondary-800 mb-4">Assessment Details</h3>
            
            <!-- Title -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-secondary-700 mb-2">Title *</label>
              <input
                v-model="assessment.title"
                type="text"
                required
                class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Enter assessment title"
              />
            </div>

            <!-- Description -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-secondary-700 mb-2">Description</label>
              <textarea
                v-model="assessment.description"
                rows="3"
                class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Describe this assessment..."
              ></textarea>
            </div>

            <!-- Status -->
            <div class="mb-4">
              <label class="block text-sm font-medium text-secondary-700 mb-2">Status</label>
              <select
                v-model="assessment.status"
                class="w-full px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="draft">Draft</option>
                <option value="published">Published</option>
              </select>
            </div>
          </div>

          <!-- Items List -->
          <div class="flex-1 overflow-hidden flex flex-col">
            <div class="p-6 border-b border-secondary-200">
              <div class="flex items-center justify-between">
                <h4 class="font-semibold text-secondary-800">Questions ({{ assessment.items?.length || 0 }})</h4>
                <button
                  @click="addNewItem"
                  class="text-primary-600 hover:text-primary-700 p-1"
                  title="Add Question"
                >
                  <font-awesome-icon icon="plus" />
                </button>
              </div>
            </div>

            <div class="flex-1 overflow-y-auto">
              <div v-if="!assessment.items?.length" class="p-6 text-center text-secondary-500">
                <font-awesome-icon icon="book" class="text-3xl mb-3 opacity-50" />
                <p>No questions yet. Click + to add your first question.</p>
              </div>

              <div v-else class="p-4 space-y-2">
                <div
                  v-for="(item, index) in assessment.items"
                  :key="item.id || item.tempId"
                  @click="selectItem(index)"
                  :class="[
                    'p-4 rounded-lg border-2 cursor-pointer transition-all',
                    selectedItemIndex === index
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-secondary-200 hover:border-primary-300 hover:bg-primary-25'
                  ]"
                >
                  <div class="flex items-start justify-between">
                    <div class="flex-1 pr-2">
                      <div class="flex items-center space-x-2 mb-2">
                        <span class="text-sm font-medium text-secondary-600">Q{{ index + 1 }}</span>
                        <span v-if="!item.content?.question?.trim()" class="text-xs text-amber-600 bg-amber-100 px-2 py-1 rounded">
                          Empty
                        </span>
                      </div>
                      <p class="text-sm text-secondary-800 line-clamp-2">
                        {{ item.content?.question || 'Untitled question' }}
                      </p>
                    </div>
                    <button
                      @click.stop="removeItem(index)"
                      class="text-secondary-400 hover:text-red-500 p-1"
                      title="Delete Question"
                    >
                      <font-awesome-icon icon="trash" class="text-xs" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Add Item Button -->
            <div class="p-4 border-t border-secondary-200">
              <button
                @click="addNewItem"
                class="w-full bg-primary-600 text-white py-3 rounded-lg font-medium hover:bg-primary-700 transition-colors flex items-center justify-center space-x-2"
              >
                <font-awesome-icon icon="plus" />
                <span>Add Question</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Right Content - Item Editor -->
        <div class="col-span-8 bg-white rounded-xl shadow-lg flex flex-col">
          <div v-if="selectedItemIndex === null" class="flex-1 flex items-center justify-center">
            <div class="text-center text-secondary-500">
              <font-awesome-icon icon="arrow-left" class="text-4xl mb-4 opacity-50" />
              <h3 class="text-xl font-medium mb-2">Select a question to edit</h3>
              <p>Choose a question from the left panel or create a new one to get started.</p>
            </div>
          </div>

          <div v-else class="flex-1 flex flex-col">
            <!-- AI Generator Section -->
            <div class="p-6 border-b border-secondary-200 bg-gradient-to-r from-accent-50 to-primary-50">
              <div class="flex items-start space-x-4">
                <div class="w-10 h-10 bg-gradient-to-br from-accent-500 to-primary-500 rounded-lg flex items-center justify-center flex-shrink-0">
                  <font-awesome-icon icon="wand-magic-sparkles" class="text-white" />
                </div>
                <div class="flex-1">
                  <h4 class="font-semibold text-secondary-800 mb-2">AI Question Generator</h4>
                  <div class="flex space-x-3">
                    <input
                      v-model="aiPrompt"
                      type="text"
                      class="flex-1 px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., Create a multiple choice question about photosynthesis for 5th graders, targeting the misconception that plants get food from the soil"
                    />
                    <button
                      @click="generateWithAI"
                      :disabled="isGenerating || !aiPrompt.trim()"
                      class="bg-gradient-to-r from-accent-600 to-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:from-accent-700 hover:to-primary-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      <font-awesome-icon v-if="isGenerating" icon="spinner" spin />
                      <font-awesome-icon v-else icon="wand-magic-sparkles" />
                      <span>{{ isGenerating ? 'Generating...' : 'Generate' }}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Question Editor -->
            <div class="flex-1 p-6 overflow-y-auto">
              <div class="space-y-6">
                <!-- Question Text -->
                <div>
                  <label class="block text-sm font-medium text-secondary-700 mb-2">Question Text *</label>
                  <textarea
                    v-model="currentItem.content.question"
                    rows="4"
                    class="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-lg"
                    placeholder="Enter your question here..."
                  ></textarea>
                </div>

                <!-- Answer Options -->
                <div>
                  <label class="block text-sm font-medium text-secondary-700 mb-3">Answer Options</label>
                  <div class="space-y-3">
                    <div
                      v-for="(option, optionIndex) in currentItem.content.options"
                      :key="optionIndex"
                      class="flex items-center space-x-3"
                    >
                      <input
                        :id="`correct-${optionIndex}`"
                        v-model="currentItem.content.answer"
                        :value="optionIndex"
                        type="radio"
                        class="text-primary-600 focus:ring-primary-500"
                      />
                      <label :for="`correct-${optionIndex}`" class="text-sm font-medium text-secondary-700 w-8">
                        {{ String.fromCharCode(65 + optionIndex) }}.
                      </label>
                      <input
                        v-model="option.text"
                        type="text"
                        class="flex-1 px-3 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        :placeholder="`Option ${String.fromCharCode(65 + optionIndex)}`"
                      />
                      <button
                        v-if="currentItem.content.options.length > 2"
                        @click="removeOption(optionIndex)"
                        class="text-red-500 hover:text-red-700 p-1"
                        title="Remove Option"
                      >
                        <font-awesome-icon icon="times" />
                      </button>
                    </div>

                    <button
                      v-if="currentItem.content.options.length < 6"
                      @click="addOption"
                      class="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center space-x-2"
                    >
                      <font-awesome-icon icon="plus" />
                      <span>Add Option</span>
                    </button>
                  </div>
                </div>

                <!-- Rationale -->
                <div>
                  <label class="block text-sm font-medium text-secondary-700 mb-2">
                    Rationale
                    <span class="text-secondary-500 font-normal">(Optional)</span>
                  </label>
                  <textarea
                    v-model="currentItem.rationale"
                    rows="3"
                    class="w-full px-4 py-3 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Explain why this is the correct answer or what misconceptions the distractors address..."
                  ></textarea>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Error/Success Messages -->
      <div v-if="message.text" :class="[
        'fixed bottom-6 right-6 px-6 py-4 rounded-lg shadow-lg z-50',
        message.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
      ]">
        {{ message.text }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'teacher',
  middleware: 'auth'
});

const route = useRoute();
const assessmentId = route.params.id as string;

// State
const assessment = ref<any>({});
const selectedItemIndex = ref<number | null>(null);
const aiPrompt = ref('');
const isGenerating = ref(false);
const isSaving = ref(false);
const isLoading = ref(true);
const error = ref('');
const message = ref<{ text: string; type: 'success' | 'error' } | { text: '' }>({ text: '' });

// Computed
const currentItem = computed(() => {
  return selectedItemIndex.value !== null && assessment.value.items 
    ? assessment.value.items[selectedItemIndex.value] 
    : null;
});

const canLaunch = computed(() => {
  return assessment.value.title?.trim() && 
         assessment.value.items?.length > 0 && 
         assessment.value.items.every((item: any) => 
           item.content?.question?.trim() && 
           item.content?.options?.every((opt: any) => opt.text?.trim()) &&
           item.content?.answer !== null && item.content?.answer !== undefined
         );
});

// Load assessment data
onMounted(async () => {
  try {
    const data = await $fetch(`/api/assessments/${assessmentId}`);
    assessment.value = data;
    
    // Initialize items if not present
    if (!assessment.value.items) {
      assessment.value.items = [];
    }
    
    // Select first item if available
    if (assessment.value.items.length > 0) {
      selectedItemIndex.value = 0;
    }
  } catch (err: any) {
    error.value = err.status === 404 
      ? 'Assessment not found or you do not have permission to access it.'
      : 'Failed to load assessment. Please try again.';
  } finally {
    isLoading.value = false;
  }
});

// Methods (same as new.vue)
const addNewItem = () => {
  const newItem = {
    tempId: Date.now(),
    item_order: assessment.value.items?.length || 0,
    item_type: 'multiple_choice',
    content: {
      question: '',
      options: [
        { text: '' },
        { text: '' },
        { text: '' },
        { text: '' }
      ],
      answer: 0
    },
    rationale: ''
  };
  
  if (!assessment.value.items) {
    assessment.value.items = [];
  }
  
  assessment.value.items.push(newItem);
  selectedItemIndex.value = assessment.value.items.length - 1;
};

const removeItem = (index: number) => {
  if (confirm('Are you sure you want to delete this question?')) {
    assessment.value.items.splice(index, 1);
    if (selectedItemIndex.value === index) {
      selectedItemIndex.value = assessment.value.items.length > 0 ? Math.max(0, index - 1) : null;
    } else if (selectedItemIndex.value !== null && selectedItemIndex.value > index) {
      selectedItemIndex.value--;
    }
  }
};

const selectItem = (index: number) => {
  selectedItemIndex.value = index;
};

const addOption = () => {
  if (currentItem.value && currentItem.value.content.options.length < 6) {
    currentItem.value.content.options.push({ text: '' });
  }
};

const removeOption = (optionIndex: number) => {
  if (currentItem.value && currentItem.value.content.options.length > 2) {
    currentItem.value.content.options.splice(optionIndex, 1);
    
    if (currentItem.value.content.answer >= optionIndex && currentItem.value.content.answer > 0) {
      currentItem.value.content.answer--;
    }
  }
};

const generateWithAI = async () => {
  if (!aiPrompt.value.trim() || !currentItem.value) return;
  
  isGenerating.value = true;
  try {
    const response = await $fetch('/api/ai/generate-item', {
      method: 'POST',
      body: { prompt: aiPrompt.value }
    });
    
    if (response.item) {
      currentItem.value.content = response.item.content;
      currentItem.value.rationale = response.item.rationale || '';
      
      showMessage('Question generated successfully!', 'success');
      aiPrompt.value = '';
    }
  } catch (error) {
    console.error('AI generation failed:', error);
    showMessage('Failed to generate question. Please try again.', 'error');
  } finally {
    isGenerating.value = false;
  }
};

const saveAssessment = async () => {
  if (!assessment.value.title?.trim()) {
    showMessage('Please enter a title for your assessment.', 'error');
    return;
  }
  
  isSaving.value = true;
  try {
    await $fetch(`/api/assessments/${assessmentId}`, {
      method: 'PUT',
      body: {
        title: assessment.value.title,
        description: assessment.value.description,
        status: assessment.value.status,
        items: assessment.value.items.map((item: any, index: number) => ({
          ...item,
          item_order: index
        }))
      }
    });
    
    showMessage('Assessment saved successfully!', 'success');
  } catch (error) {
    console.error('Save failed:', error);
    showMessage('Failed to save assessment. Please try again.', 'error');
  } finally {
    isSaving.value = false;
  }
};

const launchAssessment = async () => {
  try {
    const session = await $fetch('/api/sessions', {
      method: 'POST',
      body: { assessment_id: parseInt(assessmentId) }
    });
    
    if (session.session_id) {
      await navigateTo(`/session/${session.session_id}/host`);
    }
  } catch (error) {
    console.error('Launch failed:', error);
    showMessage('Failed to launch assessment. Please try again.', 'error');
  }
};

const deleteAssessment = async () => {
  if (!confirm('Are you sure you want to delete this assessment? This action cannot be undone.')) {
    return;
  }
  
  try {
    await $fetch(`/api/assessments/${assessmentId}`, {
      method: 'DELETE'
    });
    
    await navigateTo('/dashboard');
  } catch (error) {
    console.error('Delete failed:', error);
    showMessage('Failed to delete assessment. Please try again.', 'error');
  }
};

const showMessage = (text: string, type: 'success' | 'error') => {
  message.value = { text, type };
  setTimeout(() => {
    message.value = { text: '' };
  }, 3000);
};

useHead({
  title: computed(() => `${assessment.value.title || 'Assessment'} - CHECKPOINT`)
});
</script>

<style scoped>
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>