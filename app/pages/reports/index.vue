<template>
  <div class="space-y-8">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold text-slate-800">Reports</h1>
      <NuxtLink to="/dashboard" class="text-blue-600 hover:text-blue-700">Back to Dashboard</NuxtLink>
    </div>

    <div v-if="isLoading" class="p-8 text-center">
      <FontAwesomeIcon icon="spinner" spin class="text-blue-500 text-2xl mb-4" />
      <p class="text-slate-600">Loading your reports...</p>
    </div>

    <div v-else-if="!reports.length" class="p-12 text-center bg-white rounded-xl border border-slate-200">
      <div class="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <FontAwesomeIcon icon="chart-bar" class="text-slate-400 text-2xl" />
      </div>
      <h3 class="text-lg font-semibold text-slate-800 mb-2">No reports yet</h3>
      <p class="text-slate-600">Run a session to generate a report.</p>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="session in reports"
        :key="session.session_id"
        class="bg-white rounded-2xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow"
      >
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold text-slate-800">{{ session.assessment_title }}</h3>
          <span :class="[
            'px-2 py-1 rounded-full text-xs font-medium',
            session.status === 'finished' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
          ]">
            {{ session.status === 'finished' ? 'Completed' : 'In Progress' }}
          </span>
        </div>
        <p class="text-slate-600 text-sm mb-4">{{ session.description }}</p>
        <div class="flex items-center justify-between">
          <span class="text-xs text-slate-500">{{ new Date(session.created_at).toLocaleString() }}</span>
          <NuxtLink
            :to="`/report/${session.session_id}`"
            class="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            View Report
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'teacher',
  middleware: 'auth'
});

const { authFetch } = useApi();
const reports = ref<any[]>([]);
const isLoading = ref(true);

onMounted(async () => {
  try {
    reports.value = await authFetch('/api/reports');
  } catch (e) {
    console.error('Failed to load reports', e);
  } finally {
    isLoading.value = false;
  }
});

useHead({ title: 'Reports - CHECKPOINT' });
</script>

