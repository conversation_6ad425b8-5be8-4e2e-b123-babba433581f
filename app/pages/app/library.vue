<template>
	<section class="space-y-6">
		<div class="flex items-center justify-between">
			<h1 class="text-2xl font-semibold text-slate-900">My Library</h1>
			<input placeholder="Search" class="px-4 py-2 rounded-md border border-slate-300" />
		</div>
		<div class="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
			<AssessmentCard v-for="a in assessments" :key="a.id" :assessment="a" />
		</div>
	</section>
</template>

<script setup lang="ts">
definePageMeta({ layout: 'teacher' })

import { useAssessments } from 'shared/stores/assessments'

const { listAssessments } = useAssessments()
const assessments = ref(await listAssessments())
</script>

