<template>
	<section class="max-w-4xl">
		<h1 class="text-2xl font-semibold text-slate-900">Create Assessment</h1>
		<div class="mt-6 grid gap-6">
			<div class="bg-white border border-slate-200 rounded-xl p-5 grid md:grid-cols-2 gap-4">
				<label>
					<div class="text-sm text-slate-600">Title</div>
					<input v-model="form.title" class="mt-1 w-full px-4 py-3 rounded-md border border-slate-300" />
				</label>
				<label>
					<div class="text-sm text-slate-600">Subject</div>
					<select v-model="form.subject" class="mt-1 w-full px-4 py-3 rounded-md border border-slate-300">
						<option>Math</option>
					</select>
				</label>
				<label>
					<div class="text-sm text-slate-600">Grade</div>
					<select v-model="form.grade" class="mt-1 w-full px-4 py-3 rounded-md border border-slate-300">
						<option>Grade 6</option>
						<option>Grade 7</option>
						<option>Grade 8</option>
					</select>
				</label>
				<label class="md:col-span-2">
					<div class="text-sm text-slate-600">Standards</div>
					<select v-model="form.standardId" class="mt-1 w-full px-4 py-3 rounded-md border border-slate-300">
						<option v-for="s in standards" :key="s.id" :value="s.id">{{ s.code }} — {{ s.title }}</option>
					</select>
				</label>
			</div>
			<div class="bg-white border border-slate-200 rounded-xl p-5">
				<h2 class="font-medium text-slate-900">Misconceptions</h2>
				<div class="mt-3 grid sm:grid-cols-2 gap-3">
					<label v-for="m in misconceptions" :key="m.id" class="flex items-start gap-3 p-3 rounded-md border border-slate-200 hover:bg-slate-50">
						<input type="checkbox" v-model="form.misconceptionIds" :value="m.id" />
						<div>
							<div class="font-medium">{{ m.title }}</div>
							<div class="text-sm text-slate-600">{{ m.description }}</div>
						</div>
					</label>
				</div>
			</div>
			<div class="bg-white border border-slate-200 rounded-xl p-5">
				<h2 class="font-medium text-slate-900">Parameters</h2>
				<div class="mt-4 grid md:grid-cols-3 gap-4 items-center">
					<label class="md:col-span-1">
						<div class="text-sm text-slate-600">Num Questions</div>
						<input type="number" v-model.number="form.numQuestions" min="1" max="20" class="mt-1 w-full px-4 py-3 rounded-md border border-slate-300" />
					</label>
					<div class="md:col-span-2">
						<div class="flex items-center justify-between text-sm text-slate-600"><span>Target Difficulty</span><span>{{ form.targetDifficulty.toFixed(2) }}</span></div>
						<input type="range" v-model.number="form.targetDifficulty" step="0.01" min="0" max="1" class="w-full" />
					</div>
				</div>
				<div class="mt-4">
					<button class="px-4 py-2 rounded-md bg-slate-900 text-white" @click="generate">Generate Questions</button>
				</div>
				<div v-if="items.length" class="mt-6 space-y-4">
					<div v-for="(it, idx) in items" :key="idx" class="p-4 rounded-md border border-slate-200">
						<div class="font-medium">{{ it.stem }}</div>
						<div class="mt-2 grid gap-2">
							<div v-for="(c,ci) in it.choices" :key="ci" class="px-3 py-2 rounded-md border">{{ c }}</div>
						</div>
						<div class="mt-3 text-sm text-slate-600">Predicted Difficulty: {{ it.predicted?.difficulty?.toFixed(2) ?? '—' }} · Discrimination: {{ it.predicted?.discrimination?.toFixed(2) ?? '—' }}</div>
						<div class="mt-2 text-sm text-slate-600">Diagnostic Target: {{ it.misconceptionTitle }}</div>
					</div>
					<div class="text-right">
						<button class="px-4 py-2 rounded-md bg-orange-500 text-white" @click="save">Save to Library</button>
					</div>
				</div>
			</div>
		</div>
	</section>
</template>

<script setup lang="ts">
definePageMeta({ layout: 'teacher' })

import type { Assessment, AssessmentItem } from 'shared/types/models'
import { useStandards } from 'shared/stores/standards'
import { useMisconceptions } from 'shared/stores/misconceptions'
import { useAssessments } from 'shared/stores/assessments'

const standards = await useStandards().listStandards()
const misconceptions = await useMisconceptions().listMisconceptions()

const form = reactive({
	title: 'Untitled Assessment',
	subject: 'Math',
	grade: 'Grade 6',
	standardId: standards[0]?.id,
	misconceptionIds: [] as string[],
	numQuestions: 5,
	targetDifficulty: 0.6
})

const items = ref<(AssessmentItem & { misconceptionTitle?: string })[]>([])

async function generate(){
	const selectedMis = misconceptions.filter(m=>form.misconceptionIds.includes(m.id))
	const prompt = `Create ${form.numQuestions} multiple choice math questions for ${form.grade} aligned to ${standards.find(s=>s.id===form.standardId)?.code}. Target difficulty ${(form.targetDifficulty).toFixed(2)}. Each targets one of these misconceptions: ${selectedMis.map(m=>m.title).join('; ')}. Provide 4 options with one correct.`
	const result = await $fetch('/api/llm', { method:'POST', body: { messages: [{ role:'system', content:'You are an expert math item writer.'}, { role:'user', content: prompt }] } })
	// Expect OpenAI-format tool result; here we simulate parsing JSON blocks from model output if present, else create placeholders
	items.value = await generateMockItems(selectedMis, form.numQuestions)
}

async function save(){
	const { saveAssessment } = useAssessments()
	const assessment: Assessment = {
		id: crypto.randomUUID(),
		title: form.title,
		subject: form.subject,
		grade: form.grade,
		standardId: form.standardId!,
		misconceptionIds: [...form.misconceptionIds],
		items: items.value.map(it=>({ id: crypto.randomUUID(), stem: it.stem, choices: it.choices, answerIndex: it.answerIndex, misconceptionId: it.misconceptionId, predicted: it.predicted })),
		metrics: { difficulty: 0.6, discrimination: 0.4 },
		createdAt: Date.now()
	}
	await saveAssessment(assessment)
	navigateTo('/app/library')
}

async function generateMockItems(selectedMis: any[], count: number){
	const arr: (AssessmentItem & { misconceptionTitle?: string })[] = []
	for(let i=0;i<count;i++){
		const m = selectedMis[i % Math.max(1, selectedMis.length)] || { id: 'misc1', title:'Confusing area and perimeter' }
		arr.push({
			id: `tmp-${i}`,
			stem: `Sample question ${i+1}: What is 3/4 of 20?`,
			choices: ['10', '12', '15', '8'],
			answerIndex: 1,
			misconceptionId: m.id,
			misconceptionTitle: m.title,
			predicted: { difficulty: Math.min(0.95, Math.max(0.05, form.targetDifficulty + (Math.random()-0.5)*0.2)), discrimination: 0.4 + (Math.random()-0.5)*0.2 }
		})
	}
	return arr
}
</script>

