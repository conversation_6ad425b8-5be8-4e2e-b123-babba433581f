<template>
	<section class="max-w-4xl">
		<h1 class="text-2xl font-semibold text-slate-900">Edit Assessment</h1>
		<div v-if="assessment" class="mt-6 space-y-4">
			<label class="block">
				<div class="text-sm text-slate-600">Title</div>
				<input v-model="assessment.title" class="mt-1 w-full px-4 py-3 rounded-md border border-slate-300" />
			</label>
			<div class="space-y-3">
				<div v-for="(it,idx) in assessment.items" :key="it.id" class="p-4 border rounded-md">
					<div class="font-medium">Q{{ idx+1 }}</div>
					<textarea v-model="it.stem" class="mt-2 w-full rounded-md border p-3"></textarea>
					<div class="grid gap-2 mt-2">
						<div v-for="(c,ci) in it.choices" :key="ci" class="flex items-center gap-2">
							<input class="border rounded px-3 py-2 flex-1" v-model="it.choices[ci]" />
							<input type="radio" :name="`ans-${it.id}`" :checked="ci===it.answerIndex" @change="it.answerIndex=ci" />
						</div>
					</div>
				</div>
			</div>
			<div class="text-right">
				<button class="px-4 py-2 rounded-md bg-slate-900 text-white" @click="save">Save</button>
			</div>
		</div>
	</section>
</template>

<script setup lang="ts">
definePageMeta({ layout: 'teacher' })
import { useAssessments } from 'shared/stores/assessments'

const route = useRoute()
const id = route.params.id as string
const { getAssessment, updateAssessment } = useAssessments()
const assessment = ref(await getAssessment(id))

async function save(){
	if(!assessment.value) return
	await updateAssessment(assessment.value)
	navigateTo('/app/library')
}
</script>

