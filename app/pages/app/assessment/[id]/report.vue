<template>
	<section class="space-y-6">
		<h1 class="text-2xl font-semibold text-slate-900">Assessment Report</h1>
		<div class="grid md:grid-cols-3 gap-6">
			<div class="bg-white border border-slate-200 rounded-xl p-5">
				<div class="text-sm text-slate-600">Average Score</div>
				<div class="mt-2 text-3xl font-semibold">{{ summary.averageScore }}%</div>
			</div>
			<div class="bg-white border border-slate-200 rounded-xl p-5">
				<div class="text-sm text-slate-600">Participants</div>
				<div class="mt-2 text-3xl font-semibold">{{ summary.participants }}</div>
			</div>
			<div class="bg-white border border-slate-200 rounded-xl p-5">
				<div class="text-sm text-slate-600">Sessions</div>
				<div class="mt-2 text-3xl font-semibold">{{ summary.sessions }}</div>
			</div>
		</div>
		<div class="bg-white border border-slate-200 rounded-xl p-5">
			<h2 class="font-medium text-slate-900">Question Breakdown</h2>
			<div class="mt-4 space-y-6">
				<div v-for="(q, idx) in breakdown" :key="q.id" class="border rounded-md p-4">
					<div class="font-medium">Q{{ idx+1 }} — {{ q.stem }}</div>
					<div class="mt-2 grid sm:grid-cols-2 gap-3">
						<div v-for="(opt, oi) in q.choices" :key="oi" class="flex items-center justify-between px-3 py-2 rounded-md border">
							<span>{{ String.fromCharCode(65+oi) }}. {{ opt }}</span>
							<span class="text-sm text-slate-600">{{ (q.distribution as Record<number, number>)[oi] ?? 0 }}%</span>
						</div>
					</div>
					<div class="mt-2 text-sm text-slate-600" v-if="q.misconceptionNote">{{ q.misconceptionNote }}</div>
					<div class="mt-1 text-sm text-slate-600">p-value: {{ q.pValue.toFixed(2) }} · discrimination: {{ q.discrimination.toFixed(2) }}</div>
				</div>
			</div>
			<div class="text-right mt-4">
				<button class="px-4 py-2 rounded-md border">Export Data</button>
			</div>
		</div>
	</section>
</template>

<script setup lang="ts">
definePageMeta({ layout: 'teacher' })
const summary = reactive({ averageScore: 74, participants: 28, sessions: 5 })
const breakdown = ref([
	{ id: 'q1', stem: 'What is 3/4 of 20?', choices: ['10','12','15','8'], distribution: {0: 10, 1: 62, 2: 20, 3: 8}, misconceptionNote: '38% chose B, reflecting confusion about fraction of a whole.', pValue: 0.62, discrimination: 0.42 },
	{ id: 'q2', stem: 'Solve: 2x + 5 = 15', choices: ['x=10','x=5','x=3','x=2'], distribution: {0: 5, 1: 70, 2: 20, 3: 5}, misconceptionNote: '', pValue: 0.70, discrimination: 0.35 }
])
</script>

