<template>
	<section>
		<div class="grid gap-6 md:grid-cols-3">
			<div class="md:col-span-3">
				<h1 class="text-2xl font-semibold text-slate-900">Welcome back</h1>
				<div class="mt-3 flex gap-3">
					<NuxtLink to="/app/assessment/new" class="px-4 py-2 rounded-md bg-slate-900 text-white">+ Create New Assessment</NuxtLink>
				</div>
			</div>
			<div class="bg-white border border-slate-200 rounded-xl p-5">
				<div class="text-sm text-slate-600">Total Assessments Created</div>
				<div class="mt-2 text-3xl font-semibold">{{ stats.totalAssessments }}</div>
			</div>
			<div class="bg-white border border-slate-200 rounded-xl p-5">
				<div class="text-sm text-slate-600">Total Sessions Hosted</div>
				<div class="mt-2 text-3xl font-semibold">{{ stats.totalSessions }}</div>
			</div>
			<div class="bg-white border border-slate-200 rounded-xl p-5">
				<div class="text-sm text-slate-600">Average Score</div>
				<div class="mt-2 text-3xl font-semibold">{{ stats.averageScore }}%</div>
			</div>
			<div class="md:col-span-3 grid md:grid-cols-2 gap-6">
				<div class="bg-white border border-slate-200 rounded-xl p-5">
					<h2 class="font-medium text-slate-900">Recent Activity</h2>
					<ul class="mt-3 space-y-2 text-sm">
						<li v-for="item in recent" :key="item.id" class="flex items-center justify-between">
							<span>{{ item.title }}</span>
							<NuxtLink :to="`/app/assessment/${item.id}/report`" class="text-slate-900 underline">Open</NuxtLink>
						</li>
					</ul>
				</div>
				<div class="bg-white border border-slate-200 rounded-xl p-5">
					<h2 class="font-medium text-slate-900">Insights</h2>
					<ul class="mt-3 space-y-2 text-sm">
						<li>Highest discrimination questions ready to review.</li>
						<li>Common sticking point: Proportional reasoning.</li>
						<li>15 questions have sufficient data for precise analysis.</li>
					</ul>
				</div>
			</div>
		</div>
	</section>
</template>

<script setup lang="ts">
definePageMeta({ layout: 'teacher' })

const stats = reactive({ totalAssessments: 12, totalSessions: 8, averageScore: 76 })
const recent = ref([
	{ id: 'a1', title: 'Grade 6 Fractions — Exit Ticket' },
	{ id: 'a2', title: 'Linear Equations — Warm-up' },
	{ id: 'a3', title: 'Area vs Perimeter — Quiz' }
])
</script>

