<template>
	<div class="bg-white border border-slate-200 rounded-xl p-5 flex flex-col">
		<div class="flex-1">
			<div class="flex items-start justify-between gap-4">
				<div>
					<h3 class="font-semibold text-slate-900">{{ assessment.title }}</h3>
					<p class="text-sm text-slate-600">{{ assessment.subject }} · {{ assessment.grade }}</p>
				</div>
				<div class="text-right text-xs text-slate-500">
					<div>
						<i class="fa-solid fa-gauge-high text-orange-500"></i>
						<span class="ml-1">Difficulty {{ assessment.metrics?.difficulty ?? '—' }}</span>
					</div>
					<div class="mt-1">
						<i class="fa-solid fa-bullseye text-orange-500"></i>
						<span class="ml-1">Disc. {{ assessment.metrics?.discrimination ?? '—' }}</span>
					</div>
				</div>
			</div>
			<div class="mt-3 text-sm text-slate-600">{{ assessment.items?.length || 0 }} questions</div>
		</div>
		<div class="mt-5 grid grid-cols-2 gap-2">
			<button class="px-3 py-2 rounded-md bg-slate-900 text-white" @click="launch">Launch</button>
			<NuxtLink :to="`/app/assessment/${assessment.id}/report`" class="px-3 py-2 rounded-md border text-center">Report</NuxtLink>
			<NuxtLink :to="`/app/assessment/${assessment.id}/edit`" class="px-3 py-2 rounded-md border text-center">Edit</NuxtLink>
			<div class="relative">
				<button class="w-full px-3 py-2 rounded-md border">More</button>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import type { Assessment } from 'shared/types/models'

const props = defineProps<{ assessment: Assessment }>()

async function launch(){
	const res = await $fetch('/api/session/create', { method:'POST', body:{ assessmentId: props.assessment.id } })
	if((res as any).code){
		alert(`Session code: ${(res as any).code}`)
	}
}
</script>

