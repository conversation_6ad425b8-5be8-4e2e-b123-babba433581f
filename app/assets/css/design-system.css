/* CHECKPOINT Design System */

/* Typography Scale */
.text-h1 { @apply text-4xl lg:text-5xl font-bold text-slate-800 leading-tight; }
.text-h2 { @apply text-3xl font-bold text-slate-800; }
.text-h3 { @apply text-xl font-semibold text-slate-800; }
.text-h4 { @apply text-lg font-semibold text-slate-800; }
.text-body { @apply text-slate-600; }
.text-body-sm { @apply text-sm text-slate-600; }
.text-muted { @apply text-slate-500; }

/* Color System */
:root {
  --color-primary: rgb(37 99 235); /* blue-600 */
  --color-primary-hover: rgb(29 78 216); /* blue-700 */
  --color-secondary: rgb(79 70 229); /* indigo-600 */
  --color-secondary-hover: rgb(67 56 202); /* indigo-700 */
  --color-success: rgb(34 197 94); /* green-500 */
  --color-warning: rgb(245 158 11); /* amber-500 */
  --color-error: rgb(239 68 68); /* red-500 */
}

/* <PERSON><PERSON> Variants */
.btn-primary {
  @apply bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm;
}

.btn-secondary {
  @apply bg-slate-100 text-slate-700 px-4 py-2 rounded-lg font-medium hover:bg-slate-200 transition-colors;
}

.btn-ghost {
  @apply text-slate-600 px-4 py-2 rounded-lg font-medium hover:bg-slate-100 transition-colors;
}

.btn-danger {
  @apply bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors shadow-sm;
}

/* Card Variants */
.card {
  @apply bg-white rounded-2xl shadow-sm border border-slate-200;
}

.card-hover {
  @apply bg-white rounded-2xl shadow-sm border border-slate-200 hover:shadow-md transition-shadow;
}

.card-interactive {
  @apply bg-white rounded-2xl shadow-sm border border-slate-200 hover:shadow-md hover:border-slate-300 transition-all duration-200 cursor-pointer;
}

/* Input Variants */
.input {
  @apply px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors;
}

.input-error {
  @apply px-4 py-3 border border-red-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors;
}

/* Spacing System */
.spacing-section { @apply py-16; }
.spacing-section-sm { @apply py-8; }
.spacing-section-lg { @apply py-24; }

.spacing-container { @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8; }
.spacing-container-sm { @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8; }

/* Status Indicators */
.status-published {
  @apply px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800;
}

.status-draft {
  @apply px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800;
}

.status-active {
  @apply px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800;
}

.status-completed {
  @apply px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800;
}

/* Animation Utilities */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

/* Focus States */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

/* Utility Classes */
.text-gradient {
  @apply text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-indigo-600;
}

.bg-gradient-primary {
  @apply bg-gradient-to-r from-blue-600 to-indigo-600;
}

.shadow-soft {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
}

.border-soft {
  @apply border border-slate-200;
}

/* Responsive Grid System */
.grid-responsive {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.grid-stats {
  @apply grid grid-cols-2 md:grid-cols-4 gap-6;
}

.grid-cards {
  @apply grid grid-cols-1 lg:grid-cols-2 gap-8;
}

/* Loading States */
.loading-overlay {
  @apply fixed inset-0 bg-black/50 flex items-center justify-center z-50;
}

.loading-card {
  @apply bg-white rounded-xl p-8 flex items-center space-x-4 shadow-xl;
}

/* Error States */
.error-message {
  @apply p-4 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm;
}

.success-message {
  @apply p-4 bg-green-50 border border-green-200 rounded-lg text-green-800 text-sm;
}

/* Avatar System */
.avatar {
  @apply w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center;
}

.avatar-sm {
  @apply w-6 h-6 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center;
}

.avatar-lg {
  @apply w-12 h-12 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center;
}

/* Mobile-first Responsive Design */
@media (max-width: 768px) {
  .mobile-stack {
    @apply space-y-4;
  }
  
  .mobile-full {
    @apply w-full;
  }
  
  .mobile-center {
    @apply text-center;
  }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles would go here */
}