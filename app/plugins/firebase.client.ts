import { initializeApp } from 'firebase/app'
import { getAnalytics } from 'firebase/analytics'
import { getAuth, GoogleAuthProvider, signInWithPopup, signInAnonymously, onAuthStateChanged } from 'firebase/auth'

export default defineNuxtPlugin(()=>{
	const firebaseConfig = {
		apiKey: 'AIzaSyCYuNquZ-QhjCVqvmfZCXterbm-pU3jsoM',
		authDomain: 'viablelearning.firebaseapp.com',
		projectId: 'viablelearning',
		storageBucket: 'viablelearning.firebasestorage.app',
		messagingSenderId: '971830893184',
		appId: '1:971830893184:web:5e2892afd0b30749132465',
		measurementId: 'G-KZ00EYSW6L'
	}
	const app = initializeApp(firebaseConfig)
	if(typeof window !== 'undefined'){
		try{ getAnalytics(app) }catch(e){}
	}
	const auth = getAuth()
	const teacherUser = useState<any>('teacherUser', ()=>null)
	onAuthStateChanged(auth, (u)=>{
		teacherUser.value = u
	})

	return {
		provide: {
			auth,
			GoogleAuthProvider,
			signInWithPopup,
			signInAnonymously
		}
	}
})

