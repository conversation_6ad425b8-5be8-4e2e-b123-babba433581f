import { initializeApp, type FirebaseOptions } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { config } from '@fortawesome/fontawesome-svg-core';
import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { 
  faPlus, faWandMagicSparkles, faPlay, faEdit, faTrash, 
  faUsers, faChartBar, faSignOutAlt, faHome, faBook, 
  faCog, faSpinner, faCheck, faTimes, faArrowRight,
  faArrowLeft, faCopy, faExternalLinkAlt, faDownload,
  faClock, faPause, faStop, faChevronDown, faBars,
  faUser, faQuestionCircle, faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';

// Prevent font-awesome from auto-adding CSS
config.autoAddCss = false;

export default defineNuxtPlugin(nuxtApp => {
  // Add Font Awesome icons to library first
  library.add(
    faPlus, faWandMagicSparkles, faPlay, faEdit, faTrash,
    faUsers, faChartBar, faSignOutAlt, faHome, faBook,
    faCog, faSpinner, faCheck, faTimes, faArrowRight,
    faArrowLeft, faCopy, faExternalLinkAlt, faDownload,
    faClock, faPause, faStop, faChevronDown, faBars,
    faUser, faQuestionCircle, faExclamationTriangle
  );

  // Register FontAwesome component globally
  nuxtApp.vueApp.component('FontAwesomeIcon', FontAwesomeIcon);

  // Initialize Firebase
  const runtimeConfig = useRuntimeConfig();
  const app = initializeApp(runtimeConfig.public.firebaseConfig as FirebaseOptions);
  const auth = getAuth(app);

  // Provide auth to the rest of the app
  nuxtApp.provide('auth', auth);
});