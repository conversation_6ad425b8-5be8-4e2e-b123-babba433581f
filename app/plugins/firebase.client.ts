import { initializeApp, type FirebaseOptions } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { library } from '@fortawesome/fontawesome-svg-core';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { 
  faPlus, faWandMagicSparkles, faPlay, faEdit, faTrash, 
  faUsers, faChartBar, faSignOutAlt, faHome, faBook, 
  faCog, faSpinner, faCheck, faTimes, faArrowRight,
  faArrowLeft, faCopy, faExternalLinkAlt, faDownload,
  faClock, faPause, faStop
} from '@fortawesome/free-solid-svg-icons';

export default defineNuxtPlugin(nuxtApp => {
  // Add Font Awesome icons to library first
  library.add(
    faPlus, faWandMagicSparkles, faPlay, faEdit, faTrash,
    faUsers, faChartBar, faSignOutAlt, faHome, faBook,
    faCog, faSpinner, faCheck, faTimes, faArrowRight,
    faArrowLeft, faCopy, faExternalLinkAlt, faDownload,
    faClock, faPause, faStop
  );

  // Register FontAwesome component globally
  nuxtApp.vueApp.component('FontAwesomeIcon', FontAwesomeIcon);

  // Initialize Firebase
  const config = useRuntimeConfig();
  const app = initializeApp(config.public.firebaseConfig as FirebaseOptions);
  const auth = getAuth(app);

  // Provide auth to the rest of the app
  nuxtApp.provide('auth', auth);
});