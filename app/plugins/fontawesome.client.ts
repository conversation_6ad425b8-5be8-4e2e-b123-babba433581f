import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { 
  faPlus,
  faPlay,
  faChartBar,
  faEdit,
  faCopy,
  faTrash,
  faDownload,
  faUsers,
  faClock,
  faCheckCircle,
  faTimesCircle,
  faSpinner,
  faSignOutAlt,
  faCog,
  faHome,
  faBook,
  faDatabase,
  faQuestionCircle,
  faLightbulb,
  faBullseye,
  faChartLine,
  faEye,
  faTasks,
  faFilter,
  faSearch,
  faGraduationCap,
  faChalkboardTeacher,
  faFileAlt,
  faShare,
  faStar,
  faHeart,
  faBrain,
  faRocket,
  faTarget,
  faMagic,
  faChartPie,
  faClipboardList
} from '@fortawesome/free-solid-svg-icons'
import { faGoogle } from '@fortawesome/free-brands-svg-icons'

library.add(
  faPlus,
  faPlay,
  faChartBar,
  faEdit,
  faCopy,
  faTrash,
  faDownload,
  faUsers,
  faClock,
  faCheckCircle,
  faTimesCircle,
  faSpinner,
  faSignOutAlt,
  faCog,
  faHome,
  faBook,
  faDatabase,
  faQuestionCircle,
  faLightbulb,
  faBullseye,
  faChartLine,
  faEye,
  faTasks,
  faFilter,
  faSearch,
  faGraduationCap,
  faChalkboardTeacher,
  faFileAlt,
  faShare,
  faStar,
  faHeart,
  faBrain,
  faRocket,
  faTarget,
  faMagic,
  faChartPie,
  faClipboardList,
  faGoogle
)

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.component('FontAwesomeIcon', FontAwesomeIcon)
})