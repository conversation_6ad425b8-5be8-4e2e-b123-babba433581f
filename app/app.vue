<template>
  <div class="min-h-screen bg-gradient-to-br from-brand-blue-50 to-brand-orange-50">
    <NuxtPage />
  </div>
</template>

<script setup>
// Auto-initialize anonymous auth for students when they first visit
const { signInAnonymous, user, isStudent } = useAuth()

onMounted(async () => {
  // Only auto-initialize if not already logged in
  if (!user.value) {
    try {
      await signInAnonymous()
    } catch (error) {
      console.warn('Failed to initialize anonymous auth:', error)
    }
  }
})
</script>
