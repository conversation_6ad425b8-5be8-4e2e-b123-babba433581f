export interface User {
  id: string;
  email: string;
  displayName?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Assessment {
  id: string;
  title: string;
  description: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface Session {
  id: string;
  assessment_id: string;
  assessment_title?: string;
  description?: string;
  user_id?: string;
  join_code: string;
  status: 'waiting' | 'active' | 'paused' | 'completed';
  created_at: string;
  updated_at: string;
  total_questions?: number;
}

export interface Item {
  id: string;
  assessment_id: string;
  item_type: 'multiple_choice' | 'short_answer' | 'code';
  content: {
    question: string;
    options?: string[];
    correct_answer?: string;
    points?: number;
  };
  order_index: number;
  created_at: string;
}

export interface Participant {
  id: string;
  session_id: string;
  nickname: string;
  created_at: string;
}

export interface Response {
  id: string;
  participant_id: string;
  item_id: string;
  answer: string;
  is_correct: boolean;
  response_time: number;
  created_at: string;
}

export interface AuthUser {
  uid: string;
  email: string | null;
  displayName: string | null;
  getIdToken: () => Promise<string>;
}