import { 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
  onAuthStateChanged,
  type User,
  updateProfile
} from 'firebase/auth';

export const useAuth = () => {
  const { $auth } = useNuxtApp();
  const user = ref<User | null>(null);
  const loading = ref(true);
  const error = ref<string | null>(null);

  // Initialize auth state
  onMounted(() => {
    onAuthStateChanged($auth, (firebaseUser) => {
      user.value = firebaseUser;
      loading.value = false;
      
      // Sync user with backend when authenticated
      if (firebaseUser) {
        syncUserWithBackend();
      }
    });
  });

  const syncUserWithBackend = async () => {
    try {
      const token = await user.value?.getIdToken();
      if (token) {
        await $fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
      }
    } catch (err) {
      console.error('Failed to sync user with backend:', err);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      error.value = null;
      loading.value = true;
      const credential = await signInWithEmailAndPassword($auth, email, password);
      user.value = credential.user;
      return credential.user;
    } catch (err: any) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const signUp = async (email: string, password: string, displayName: string) => {
    try {
      error.value = null;
      loading.value = true;
      const credential = await createUserWithEmailAndPassword($auth, email, password);
      
      // Update display name
      await updateProfile(credential.user, { displayName });
      
      user.value = credential.user;
      return credential.user;
    } catch (err: any) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const signInWithGoogle = async () => {
    try {
      error.value = null;
      loading.value = true;
      const provider = new GoogleAuthProvider();
      const credential = await signInWithPopup($auth, provider);
      user.value = credential.user;
      return credential.user;
    } catch (err: any) {
      error.value = err.message;
      throw err;
    } finally {
      loading.value = false;
    }
  };

  const logOut = async () => {
    try {
      await signOut($auth);
      user.value = null;
      await navigateTo('/');
    } catch (err: any) {
      error.value = err.message;
    }
  };

  const isAuthenticated = computed(() => !!user.value);

  return {
    user: readonly(user),
    loading: readonly(loading),
    error: readonly(error),
    isAuthenticated,
    signIn,
    signUp,
    signInWithGoogle,
    logOut
  };
};