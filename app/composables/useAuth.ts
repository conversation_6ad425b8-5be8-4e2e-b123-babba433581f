// Auth Composable - Vue composition API wrapper for AuthViewModel
import type { UserRole } from '~/shared/types/auth'
import { AuthViewModel } from '~/viewmodels/AuthViewModel'

let authViewModel: AuthViewModel | null = null

export const useAuth = () => {
  const { $firebaseAuth } = useNuxtApp()

  // Initialize ViewModel once
  if (!authViewModel) {
    authViewModel = new AuthViewModel($firebaseAuth)
  }

  return {
    // Reactive state (readonly for external use)
    user: readonly(authViewModel.user),
    userProfile: readonly(authViewModel.userProfile),
    loading: readonly(authViewModel.loading),
    error: readonly(authViewModel.error),
    
    // Actions
    signInWithEmail: authViewModel.signInWithEmail.bind(authViewModel),
    createAccountWithEmail: authViewModel.createAccountWithEmail.bind(authViewModel),
    signInWithGoogle: authViewModel.signInWithGoogle.bind(authViewModel),
    signInAnonymous: authViewModel.signInAnonymous.bind(authViewModel),
    logout: authViewModel.logout.bind(authViewModel),
    
    // Computed getters
    isTeacher: computed(() => authViewModel!.isTeacher),
    isStudent: computed(() => authViewModel!.isStudent),
    userRole: computed(() => authViewModel!.userRole)
  }
}