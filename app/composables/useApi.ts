export const useApi = () => {
  const { getAuthHeaders } = useAuth();

  const authFetch = async <T = any>(url: string, options: any = {}): Promise<T> => {
    let headers = await getAuthHeaders();
    if (!headers.Authorization) {
      // Wait briefly in case auth is still initializing
      await new Promise((r) => setTimeout(r, 50));
      headers = await getAuthHeaders();
    }
    return $fetch<T>(url, {
      ...options,
      headers: {
        ...headers,
        ...options.headers
      }
    });
  };

  return {
    authFetch
  };
};