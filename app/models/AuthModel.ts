// Auth Models - Data structures and business logic
import type { UserRole } from '~/shared/types/auth'

export interface AuthUserModel {
  uid: string
  email: string | null
  displayName: string | null
  photoURL: string | null
  isAnonymous: boolean
}

export interface UserProfileModel {
  id: string
  firebaseUid: string
  email: string
  displayName?: string
  role: UserRole
  profilePictureUrl?: string
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  isActive: boolean
}

export class AuthModel {
  static async createUser(userData: {
    firebaseUid: string
    email: string
    displayName?: string
    profilePictureUrl?: string
    role?: UserRole
  }): Promise<{ user: UserProfileModel; created: boolean }> {
    return await $fetch('/api/auth/create-user', {
      method: 'POST',
      body: userData
    })
  }

  static async createAnonymousStudent(firebaseUid: string): Promise<any> {
    return await $fetch('/api/auth/create-anonymous-student', {
      method: 'POST',
      body: { firebaseUid }
    })
  }

  static async getUserProfile(firebaseUid: string): Promise<UserProfileModel> {
    return await $fetch(`/api/auth/user/${firebaseUid}`)
  }
}