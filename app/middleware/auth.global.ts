export default defineNuxtRouteMiddleware((to) => {
  // Skip auth middleware on server side
  if (process.server) return;
  
  const { user, loading } = useAuth();
  
  // Wait for auth to be initialized
  if (loading.value) return;
  
  // Define page types
  const publicPages = ['/', '/join'];
  const authPages = ['/login', '/register'];
  const teacherPages = ['/dashboard', '/assessment', '/session', '/reports'];
  const studentPages = ['/session'];
  
  const isPublicPage = publicPages.includes(to.path);
  const isAuthPage = authPages.includes(to.path);
  const isTeacherPage = teacherPages.some(page => to.path.startsWith(page));
  const isStudentPage = to.path.startsWith('/session') && to.path.includes('/play');
  
  // Handle authentication logic
  if (user.value) {
    // Authenticated user
    if (isAuthPage) {
      // Redirect authenticated users away from auth pages
      return navigateTo('/dashboard');
    }
  } else {
    // Unauthenticated user
    if (isTeacherPage) {
      // Teacher pages require authentication
      return navigateTo('/login');
    }
    
    if (isStudentPage) {
      // Student pages require at least session info
      const participantName = process.client ? sessionStorage.getItem('participantNickname') : null;
      if (!participantName) {
        return navigateTo('/join');
      }
    }
  }
});