export default defineNuxtRouteMiddleware((to) => {
  // Skip auth middleware on server side
  if (process.server) return;
  
  const { user, loading } = useAuth();
  
  // Wait for auth to be initialized
  if (loading.value) return;
  
  const isAuthPage = ['/login', '/register'].includes(to.path);
  const isPublicPage = ['/', '/join'].includes(to.path);
  const requiresAuth = !isPublicPage && !isAuthPage;
  
  // Redirect authenticated users away from auth pages
  if (isAuthPage && user.value) {
    return navigateTo('/dashboard');
  }
  
  // Redirect unauthenticated users to login for protected pages
  if (requiresAuth && !user.value) {
    return navigateTo('/login');
  }
});