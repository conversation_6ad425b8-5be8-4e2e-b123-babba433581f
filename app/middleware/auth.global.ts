export default defineNuxtRouteMiddleware(async (to) => {
  // Only run on client for auth state
  if (process.server) return;

  const { user, loading } = useAuth();

  // If auth still initializing, wait briefly and re-evaluate to avoid flicker redirects
  if (loading.value) {
    await new Promise((resolve) => setTimeout(resolve, 50));
  }

  const authUser = user.value;

  // Define page types
  const publicPages = ['/', '/join'];
  const authPages = ['/login', '/register'];
  const teacherPages = ['/dashboard', '/assessment', '/session', '/reports'];
  const studentPages = ['/session'];

  const isAuthPage = authPages.includes(to.path);
  const isTeacherPage = teacherPages.some(page => to.path.startsWith(page));
  const isStudentPage = to.path.startsWith('/session') && to.path.includes('/play');

  if (authUser) {
    // Authenticated: prevent visiting auth pages
    if (isAuthPage) return navigateTo('/dashboard');
    return;
  }

  // Unauthenticated: guard teacher pages
  if (isTeacherPage) return navigateTo('/login');

  // Student play route requires session info
  if (isStudentPage) {
    const participantName = process.client ? sessionStorage.getItem('participantNickname') : null;
    if (!participantName) return navigateTo('/join');
  }
});