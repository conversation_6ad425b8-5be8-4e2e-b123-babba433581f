<template>
  <div class="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100">
    <header class="bg-white shadow-sm border-b border-primary-100">
      <div class="container mx-auto px-4 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <font-awesome-icon icon="check" class="text-white text-sm" />
            </div>
            <NuxtLink to="/" class="text-2xl font-bold text-secondary-800 hover:text-primary-600 transition-colors">
              CHECKPOINT
            </NuxtLink>
          </div>
        </div>
      </div>
    </header>

    <main class="container mx-auto px-4 py-8">
      <slot />
    </main>

    <footer class="bg-white border-t border-secondary-200 mt-16">
      <div class="container mx-auto px-4 py-6">
        <div class="text-center text-secondary-600">
          <p>&copy; 2025 CHECKPOINT. Advanced Educational Assessment Platform.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Basic layout for student-facing pages
</script>