<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
    <!-- Navigation Header -->
    <header class="bg-white/95 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <NuxtLink to="/" class="flex items-center space-x-3 hover:opacity-90 transition-opacity">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-sm">
              <FontAwesomeIcon icon="check" class="text-white text-lg" />
            </div>
            <span class="text-xl font-bold text-slate-800">CHECKPOINT</span>
          </NuxtLink>

          <!-- Navigation Links -->
          <div class="flex items-center space-x-6">
            <NuxtLink
              to="/join"
              class="text-slate-600 hover:text-blue-600 font-medium transition-colors"
            >
              Join Assessment
            </NuxtLink>
            <NuxtLink
              to="/login"
              class="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
            >
              Teacher Login
            </NuxtLink>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main>
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-slate-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Brand -->
          <div class="space-y-4">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon="check" class="text-white text-sm" />
              </div>
              <span class="text-lg font-bold text-slate-800">CHECKPOINT</span>
            </div>
            <p class="text-slate-600 text-sm">
              Advanced educational assessment platform powered by AI to diagnose student misconceptions.
            </p>
          </div>

          <!-- Quick Links -->
          <div class="space-y-4">
            <h3 class="font-semibold text-slate-800">Quick Links</h3>
            <div class="space-y-2">
              <NuxtLink to="/join" class="block text-slate-600 hover:text-blue-600 transition-colors text-sm">
                Join Assessment
              </NuxtLink>
              <NuxtLink to="/login" class="block text-slate-600 hover:text-blue-600 transition-colors text-sm">
                Teacher Login
              </NuxtLink>
              <NuxtLink to="/register" class="block text-slate-600 hover:text-blue-600 transition-colors text-sm">
                Sign Up
              </NuxtLink>
            </div>
          </div>

          <!-- Contact -->
          <div class="space-y-4">
            <h3 class="font-semibold text-slate-800">Support</h3>
            <p class="text-slate-600 text-sm">
              Need help? Contact our support team for assistance with your assessments.
            </p>
          </div>
        </div>

        <div class="border-t border-slate-200 mt-8 pt-8 text-center">
          <p class="text-slate-500 text-sm">
            &copy; 2025 CHECKPOINT. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
// Default layout for public pages (home, login, register, join)
</script>