<template>
  <div class="min-h-screen bg-secondary-50">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 w-64 bg-white shadow-lg border-r border-secondary-200">
      <div class="flex flex-col h-full">
        <!-- Logo -->
        <div class="p-6 border-b border-secondary-200">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-lg flex items-center justify-center">
              <FontAwesomeIcon icon="check" class="text-white text-lg" />
            </div>
            <h1 class="text-xl font-bold text-secondary-800">CHECKPOINT</h1>
          </div>
        </div>

        <!-- Navigation -->
        <nav class="flex-1 p-4 space-y-2">
          <NuxtLink 
            to="/dashboard" 
            class="flex items-center space-x-3 px-4 py-3 rounded-lg text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors"
            :class="{ 'bg-primary-50 text-primary-700': $route.path === '/dashboard' }"
          >
            <FontAwesomeIcon icon="home" />
            <span class="font-medium">Dashboard</span>
          </NuxtLink>

          <NuxtLink 
            to="/assessments" 
            class="flex items-center space-x-3 px-4 py-3 rounded-lg text-secondary-700 hover:bg-primary-50 hover:text-primary-700 transition-colors"
            :class="{ 'bg-primary-50 text-primary-700': $route.path.startsWith('/assessment') }"
          >
            <FontAwesomeIcon icon="book" />
            <span class="font-medium">Assessments</span>
          </NuxtLink>
        </nav>

        <!-- User Info -->
        <div class="p-4 border-t border-secondary-200">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-secondary-800">{{ user?.displayName || user?.email }}</p>
              <p class="text-xs text-secondary-600">Teacher</p>
            </div>
            <button 
              @click="logOut"
              class="p-2 rounded-lg text-secondary-600 hover:bg-secondary-100 hover:text-secondary-800 transition-colors"
              title="Sign Out"
            >
              <FontAwesomeIcon icon="sign-out-alt" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="ml-64">
      <header class="bg-white shadow-sm border-b border-secondary-200">
        <div class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-2xl font-bold text-secondary-800">{{ pageTitle }}</h2>
              <p class="text-secondary-600 mt-1">{{ pageSubtitle }}</p>
            </div>
            <div class="flex items-center space-x-3">
              <slot name="header-actions" />
            </div>
          </div>
        </div>
      </header>

      <main class="p-8">
        <div class="max-w-7xl mx-auto">
          <slot />
        </div>
      </main>
    </div>

    <!-- Loading Overlay -->
    <div v-if="loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-8 flex items-center space-x-4">
        <FontAwesomeIcon icon="spinner" spin class="text-primary-500 text-2xl" />
        <span class="text-secondary-800 font-medium">Loading...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { user, logOut, loading } = useAuth();

// Redirect to login if not authenticated
watch(loading, (newLoading) => {
  if (!newLoading && !user.value) {
    navigateTo('/login');
  }
});

// Page title management
const pageTitle = computed(() => {
  const route = useRoute();
  switch (route.name) {
    case 'dashboard': return 'Dashboard';
    case 'assessment-id': return 'Assessment Editor';
    case 'assessment-new': return 'New Assessment';
    case 'session-id-host': return 'Session Host';
    case 'report-session_id': return 'Assessment Report';
    default: return 'CHECKPOINT';
  }
});

const pageSubtitle = computed(() => {
  const route = useRoute();
  switch (route.name) {
    case 'dashboard': return 'Welcome back! Manage your assessments and view insights.';
    case 'assessment-id': return 'Design diagnostic questions and customize your assessment.';
    case 'assessment-new': return 'Create a new assessment to diagnose student misconceptions.';
    case 'session-id-host': return 'Monitor student participation and control the session.';
    case 'report-session_id': return 'Analyze student responses and diagnostic insights.';
    default: return '';
  }
});

// Ensure user is authenticated
onMounted(() => {
  if (!loading.value && !user.value) {
    navigateTo('/login');
  }
});
</script>