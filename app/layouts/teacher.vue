<template>
  <div class="min-h-screen bg-slate-50">
    <!-- Top Navigation Bar -->
    <header class="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-40">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <!-- Left: Logo and Page Title -->
          <div class="flex items-center space-x-6">
            <NuxtLink to="/dashboard" class="flex items-center space-x-3 hover:opacity-90 transition-opacity">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-sm">
                <FontAwesomeIcon icon="check" class="text-white text-lg" />
              </div>
              <span class="text-xl font-bold text-slate-800">CHECKPOINT</span>
            </NuxtLink>
            
            <div class="hidden md:block border-l border-slate-200 pl-6">
              <h1 class="text-lg font-semibold text-slate-800">{{ pageTitle }}</h1>
              <p class="text-sm text-slate-600">{{ pageSubtitle }}</p>
            </div>
          </div>

          <!-- Right: Navigation and User Menu -->
          <div class="flex items-center space-x-4">
            <!-- Main Navigation -->
            <nav class="hidden md:flex items-center space-x-1">
              <NuxtLink 
                to="/dashboard" 
                class="flex items-center space-x-2 px-3 py-2 rounded-lg text-slate-600 hover:text-blue-600 hover:bg-blue-50 transition-colors"
                :class="{ 'text-blue-600 bg-blue-50': $route.path === '/dashboard' }"
              >
                <FontAwesomeIcon icon="home" class="w-4 h-4" />
                <span class="font-medium">Dashboard</span>
              </NuxtLink>

              <NuxtLink 
                to="/assessment/new" 
                class="flex items-center space-x-2 px-3 py-2 rounded-lg text-slate-600 hover:text-blue-600 hover:bg-blue-50 transition-colors"
                :class="{ 'text-blue-600 bg-blue-50': $route.path.startsWith('/assessment') }"
              >
                <FontAwesomeIcon icon="plus" class="w-4 h-4" />
                <span class="font-medium">New Assessment</span>
              </NuxtLink>
            </nav>

            <!-- User Menu -->
            <div class="relative" v-click-outside="() => showUserMenu = false">
              <button 
                @click="showUserMenu = !showUserMenu"
                class="flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-slate-100 transition-colors"
              >
                <!-- User Avatar -->
                <div class="w-8 h-8 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-full flex items-center justify-center">
                  <span class="text-white text-sm font-medium">
                    {{ getUserInitials(user?.displayName || user?.email) }}
                  </span>
                </div>
                
                <!-- User Info -->
                <div class="text-left hidden sm:block">
                  <p class="text-sm font-medium text-slate-800">{{ user?.displayName || 'Teacher' }}</p>
                  <p class="text-xs text-slate-600">{{ user?.email }}</p>
                </div>
                
                <FontAwesomeIcon 
                  icon="chevron-down" 
                  class="w-4 h-4 text-slate-400 transition-transform"
                  :class="{ 'rotate-180': showUserMenu }"
                />
              </button>

              <!-- Dropdown Menu -->
              <div 
                v-show="showUserMenu"
                class="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-slate-200 py-2 z-50"
              >
                <div class="px-4 py-2 border-b border-slate-100">
                  <p class="text-sm font-medium text-slate-800">{{ user?.displayName || 'Teacher' }}</p>
                  <p class="text-xs text-slate-600">{{ user?.email }}</p>
                </div>
                
                <NuxtLink 
                  to="/profile" 
                  class="flex items-center space-x-3 px-4 py-2 text-slate-700 hover:bg-slate-50 transition-colors"
                >
                  <FontAwesomeIcon icon="user" class="w-4 h-4" />
                  <span class="text-sm">Profile Settings</span>
                </NuxtLink>
                
                <NuxtLink 
                  to="/help" 
                  class="flex items-center space-x-3 px-4 py-2 text-slate-700 hover:bg-slate-50 transition-colors"
                >
                  <FontAwesomeIcon icon="question-circle" class="w-4 h-4" />
                  <span class="text-sm">Help & Support</span>
                </NuxtLink>
                
                <div class="border-t border-slate-100 mt-2 pt-2">
                  <button 
                    @click="handleLogout"
                    class="flex items-center space-x-3 px-4 py-2 text-red-600 hover:bg-red-50 transition-colors w-full"
                  >
                    <FontAwesomeIcon icon="sign-out-alt" class="w-4 h-4" />
                    <span class="text-sm">Sign Out</span>
                  </button>
                </div>
              </div>
            </div>

            <!-- Mobile Menu Button -->
            <button 
              @click="showMobileMenu = !showMobileMenu"
              class="md:hidden p-2 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors"
            >
              <FontAwesomeIcon icon="bars" class="w-5 h-5" />
            </button>
          </div>
        </div>

        <!-- Mobile Navigation -->
        <div v-show="showMobileMenu" class="md:hidden border-t border-slate-200 mt-4 pt-4">
          <nav class="space-y-2">
            <NuxtLink 
              to="/dashboard" 
              class="flex items-center space-x-3 px-3 py-2 rounded-lg text-slate-700 hover:bg-slate-100 transition-colors"
              @click="showMobileMenu = false"
            >
              <FontAwesomeIcon icon="home" class="w-4 h-4" />
              <span>Dashboard</span>
            </NuxtLink>
            
            <NuxtLink 
              to="/assessment/new" 
              class="flex items-center space-x-3 px-3 py-2 rounded-lg text-slate-700 hover:bg-slate-100 transition-colors"
              @click="showMobileMenu = false"
            >
              <FontAwesomeIcon icon="plus" class="w-4 h-4" />
              <span>New Assessment</span>
            </NuxtLink>
          </nav>
        </div>
      </div>
    </header>

    <!-- Main Content Area -->
    <main class="max-w-7xl mx-auto px-6 py-8">
      <!-- Page Header Actions Slot -->
      <div v-if="$slots['header-actions']" class="mb-8">
        <slot name="header-actions" />
      </div>
      
      <!-- Main Content -->
      <slot />
    </main>

    <!-- Loading Overlay -->
    <div v-if="loading" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white rounded-xl p-8 flex items-center space-x-4 shadow-xl">
        <FontAwesomeIcon icon="spinner" spin class="text-blue-600 text-2xl" />
        <span class="text-slate-800 font-medium">Loading...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const { user, logOut, loading } = useAuth();
const showUserMenu = ref(false);
const showMobileMenu = ref(false);

// Close menus when clicking outside
const vClickOutside = {
  beforeMount(el: HTMLElement, binding: any) {
    el.clickOutsideEvent = (event: Event) => {
      if (!(el === event.target || el.contains(event.target as Node))) {
        binding.value();
      }
    };
    document.addEventListener('click', el.clickOutsideEvent);
  },
  unmounted(el: HTMLElement) {
    document.removeEventListener('click', el.clickOutsideEvent);
  }
};

// Get user initials for avatar
const getUserInitials = (name: string | null) => {
  if (!name) return 'T';
  const parts = name.split(' ');
  if (parts.length >= 2) {
    return (parts[0][0] + parts[1][0]).toUpperCase();
  }
  return name.substring(0, 2).toUpperCase();
};

// Handle logout
const handleLogout = async () => {
  showUserMenu.value = false;
  await logOut();
};

// Page title management
const pageTitle = computed(() => {
  const route = useRoute();
  switch (route.name) {
    case 'dashboard': return 'Dashboard';
    case 'assessment-id': return 'Edit Assessment';
    case 'assessment-new': return 'New Assessment';
    case 'session-id-host': return 'Host Session';
    case 'report-session_id': return 'Session Report';
    default: return 'CHECKPOINT';
  }
});

const pageSubtitle = computed(() => {
  const route = useRoute();
  switch (route.name) {
    case 'dashboard': return 'Manage your assessments and view insights';
    case 'assessment-id': return 'Design questions and customize your assessment';
    case 'assessment-new': return 'Create a new diagnostic assessment';
    case 'session-id-host': return 'Monitor student participation and control the session';
    case 'report-session_id': return 'Analyze student responses and insights';
    default: return '';
  }
});

// Authentication guard
onMounted(() => {
  if (!loading.value && !user.value) {
    navigateTo('/login');
  }
});

watch(loading, (newLoading) => {
  if (!newLoading && !user.value) {
    navigateTo('/login');
  }
});
</script>