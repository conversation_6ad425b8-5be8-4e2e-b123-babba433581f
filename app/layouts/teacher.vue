<template>
	<div class="min-h-screen bg-slate-50 text-slate-800 font-poppins">
		<div class="flex">
			<aside class="hidden md:flex md:w-64 shrink-0 h-screen sticky top-0 border-r border-slate-200 bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60">
				<nav class="p-6 flex flex-col gap-4 w-full">
					<div class="flex items-center gap-3 mb-4">
						<i class="fa-solid fa-bullseye text-orange-500 text-xl"></i>
						<span class="font-semibold">CHECKPOINT</span>
					</div>
					<NuxtLink to="/app/dashboard" class="px-3 py-2 rounded hover:bg-slate-100"><i class="fa-solid fa-gauge-high mr-2"></i> Dashboard</NuxtLink>
					<NuxtLink to="/app/library" class="px-3 py-2 rounded hover:bg-slate-100"><i class="fa-solid fa-folder-open mr-2"></i> My Library</NuxtLink>
					<NuxtLink to="/app/settings" class="px-3 py-2 rounded hover:bg-slate-100"><i class="fa-solid fa-gear mr-2"></i> Settings</NuxtLink>
					<NuxtLink to="/" class="px-3 py-2 rounded hover:bg-slate-100"><i class="fa-solid fa-arrow-right-from-bracket mr-2"></i> Sign out</NuxtLink>
				</nav>
			</aside>
			<main class="flex-1">
				<header class="border-b border-slate-200 bg-white/80 backdrop-blur supports-[backdrop-filter]:bg-white/60 sticky top-0 z-40">
					<div class="px-6 py-4 flex items-center justify-between">
						<div class="md:hidden flex items-center gap-3">
							<i class="fa-solid fa-bullseye text-orange-500 text-xl"></i>
							<span class="font-semibold">CHECKPOINT</span>
						</div>
						<div class="text-sm text-slate-600">Signed in</div>
					</div>
				</header>
				<div class="p-6">
					<slot />
				</div>
			</main>
		</div>
	</div>
</template>

