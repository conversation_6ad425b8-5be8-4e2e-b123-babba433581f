<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
    <!-- Simple Header for Students -->
    <header class="bg-white/95 backdrop-blur-sm border-b border-slate-200 sticky top-0 z-40">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-sm">
              <FontAwesomeIcon icon="check" class="text-white text-lg" />
            </div>
            <span class="text-xl font-bold text-slate-800">CHECKPOINT</span>
          </div>

          <!-- Session Info -->
          <div v-if="sessionInfo" class="flex items-center space-x-4">
            <div class="text-right">
              <p class="text-sm font-medium text-slate-800">{{ sessionInfo.participantName }}</p>
              <p class="text-xs text-slate-600">{{ sessionInfo.sessionTitle }}</p>
            </div>
            
            <!-- Exit Button -->
            <button 
              @click="handleExit"
              class="p-2 rounded-lg text-slate-600 hover:bg-slate-100 transition-colors"
              title="Exit Assessment"
            >
              <FontAwesomeIcon icon="times" class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main>
      <slot />
    </main>

    <!-- Exit Confirmation Modal -->
    <div v-if="showExitModal" class="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div class="bg-white rounded-xl p-8 max-w-md mx-4 shadow-xl">
        <div class="text-center">
          <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <FontAwesomeIcon icon="exclamation-triangle" class="text-red-600 text-xl" />
          </div>
          
          <h3 class="text-lg font-semibold text-slate-800 mb-2">Exit Assessment?</h3>
          <p class="text-slate-600 mb-6">
            Are you sure you want to leave this assessment? Your progress may be lost.
          </p>
          
          <div class="flex space-x-3">
            <button 
              @click="showExitModal = false"
              class="flex-1 bg-slate-100 text-slate-700 px-4 py-2 rounded-lg font-medium hover:bg-slate-200 transition-colors"
            >
              Cancel
            </button>
            <button 
              @click="confirmExit"
              class="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 transition-colors"
            >
              Exit Assessment
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const showExitModal = ref(false);

// Session info can be passed as props or retrieved from storage
const sessionInfo = ref<{
  participantName: string;
  sessionTitle: string;
} | null>(null);

// Get session info from storage if available
onMounted(() => {
  const participantName = sessionStorage.getItem('participantNickname');
  const sessionTitle = sessionStorage.getItem('sessionTitle') || 'Assessment Session';
  
  if (participantName) {
    sessionInfo.value = {
      participantName,
      sessionTitle
    };
  }
});

const handleExit = () => {
  showExitModal.value = true;
};

const confirmExit = async () => {
  // Clear session storage
  sessionStorage.removeItem('participantNickname');
  sessionStorage.removeItem('joinCode');
  sessionStorage.removeItem('sessionTitle');
  
  // Navigate to home
  await navigateTo('/');
};
</script>