<script setup lang="ts">
import { useAuthStore } from '~/stores/auth'
const auth = useAuthStore()
onMounted(() => auth.init())
</script>

<template>
  <main class="p-6">
    <h1 class="text-2xl font-semibold">CHECKPOINT</h1>
    <p class="text-muted-foreground mt-2">Nuxt + Cloudflare Pages foundation is ready.</p>
    <div v-if="auth.profile" class="mt-4 text-sm">Signed in as: {{ auth.profile.email }} ({{ auth.profile.role }})</div>
  </main>
  
</template>


