-- migrations/0001_initial_schema.sql

-- User table for teachers/researchers
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY, -- Firebase UID
    email TEXT NOT NULL UNIQUE,
    display_name TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Assessments: The "quiz" or "test" container
CREATE TABLE IF NOT EXISTS assessments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'draft', -- 'draft', 'published'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Items: Individual questions within an assessment
CREATE TABLE IF NOT EXISTS items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    assessment_id INTEGER NOT NULL,
    item_order INTEGER NOT NULL,
    item_type TEXT NOT NULL DEFAULT 'multiple_choice', -- 'multiple_choice', 'free_text'
    content JSON NOT NULL, -- { "question": "text", "options": [{"text": "A"}, ...], "answer": 0 }
    psychometric_data JSON, -- { "difficulty": 0.5, "discrimination": 1.2 }
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE
);

-- Sessions: A live instance of an assessment
CREATE TABLE IF NOT EXISTS sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    assessment_id INTEGER NOT NULL,
    join_code TEXT NOT NULL UNIQUE, -- 6-character alphanumeric code
    status TEXT DEFAULT 'waiting', -- 'waiting', 'active', 'finished'
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE
);

-- Participants: Anonymous students in a session
CREATE TABLE IF NOT EXISTS participants (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER NOT NULL,
    nickname TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
);

-- Responses: A participant's answer to an item
CREATE TABLE IF NOT EXISTS responses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    participant_id INTEGER NOT NULL,
    item_id INTEGER NOT NULL,
    response_data JSON NOT NULL, -- { "answer_index": 1, "text": "B" }
    is_correct BOOLEAN,
    submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (participant_id) REFERENCES participants(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE CASCADE
);

-- Logs: For analytics and debugging
CREATE TABLE IF NOT EXISTS logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id TEXT, -- Can be NULL for system or student events
    event_type TEXT NOT NULL, -- e.g., 'USER_LOGIN', 'ASSESSMENT_CREATED', 'AI_ITEM_GENERATED'
    details JSON,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);