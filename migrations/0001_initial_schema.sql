-- Migration: 0001_initial_schema.sql
-- Create comprehensive database schema for CHECKPOINT educational platform

-- Users table for teachers and researchers
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    firebase_uid TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    display_name TEXT,
    role TEXT CHECK (role IN ('teacher', 'researcher')) NOT NULL DEFAULT 'teacher',
    profile_picture_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_at DATETIME,
    is_active BOOLEAN DEFAULT TRUE
);

-- Anonymous students table (Firebase anonymous auth)
CREATE TABLE anonymous_students (
    id TEXT PRIMARY KEY,
    firebase_uid TEXT UNIQUE NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_seen_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Teaching standards/curricula
CREATE TABLE standards (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    subject TEXT NOT NULL,
    grade_level TEXT NOT NULL,
    standard_code TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Common misconceptions/knowledge gaps
CREATE TABLE misconceptions (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    standard_id TEXT,
    subject TEXT NOT NULL,
    grade_level TEXT NOT NULL,
    category TEXT, -- conceptual, procedural, etc.
    frequency_score REAL DEFAULT 0.0, -- how common this misconception is
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (standard_id) REFERENCES standards(id)
);

-- Assessment templates/tests
CREATE TABLE assessments (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    subject TEXT NOT NULL,
    grade_level TEXT NOT NULL,
    created_by TEXT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    target_difficulty REAL DEFAULT 0.5, -- 0-1 scale
    estimated_duration INTEGER, -- minutes
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Individual questions in the question bank
CREATE TABLE questions (
    id TEXT PRIMARY KEY,
    assessment_id TEXT,
    question_text TEXT NOT NULL,
    question_type TEXT CHECK (question_type IN ('multiple_choice', 'true_false', 'short_answer')) NOT NULL DEFAULT 'multiple_choice',
    correct_answer TEXT NOT NULL,
    explanation TEXT,
    difficulty REAL, -- calculated p-value (0-1, where 1 is easy)
    discrimination REAL, -- calculated discrimination index
    point_biserial REAL, -- point-biserial correlation
    standard_id TEXT,
    misconception_id TEXT,
    created_by TEXT NOT NULL,
    is_validated BOOLEAN DEFAULT FALSE, -- has enough data for reliable psychometric measures
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (assessment_id) REFERENCES assessments(id),
    FOREIGN KEY (standard_id) REFERENCES standards(id),
    FOREIGN KEY (misconception_id) REFERENCES misconceptions(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Multiple choice options
CREATE TABLE question_options (
    id TEXT PRIMARY KEY,
    question_id TEXT NOT NULL,
    option_letter TEXT NOT NULL, -- A, B, C, D
    option_text TEXT NOT NULL,
    is_correct BOOLEAN DEFAULT FALSE,
    misconception_id TEXT, -- what misconception this distractor targets
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE,
    FOREIGN KEY (misconception_id) REFERENCES misconceptions(id)
);

-- Live assessment sessions
CREATE TABLE sessions (
    id TEXT PRIMARY KEY,
    assessment_id TEXT NOT NULL,
    session_code TEXT UNIQUE NOT NULL, -- 6-digit code for students
    title TEXT,
    created_by TEXT NOT NULL,
    status TEXT CHECK (status IN ('waiting', 'active', 'completed', 'archived')) DEFAULT 'waiting',
    start_time DATETIME,
    end_time DATETIME,
    max_participants INTEGER DEFAULT 100,
    allow_late_join BOOLEAN DEFAULT TRUE,
    show_results_immediately BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (assessment_id) REFERENCES assessments(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- Student participation in sessions
CREATE TABLE session_participants (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    student_id TEXT NOT NULL, -- references anonymous_students.id
    nickname TEXT NOT NULL,
    joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (session_id) REFERENCES sessions(id),
    FOREIGN KEY (student_id) REFERENCES anonymous_students(id)
);

-- Individual student responses
CREATE TABLE student_responses (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    participant_id TEXT NOT NULL,
    question_id TEXT NOT NULL,
    selected_answer TEXT, -- the option letter or text they selected
    is_correct BOOLEAN,
    response_time_seconds INTEGER, -- time taken to answer
    submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(id),
    FOREIGN KEY (participant_id) REFERENCES session_participants(id),
    FOREIGN KEY (question_id) REFERENCES questions(id)
);

-- Analytics and aggregated data for teacher insights
CREATE TABLE question_analytics (
    id TEXT PRIMARY KEY,
    question_id TEXT NOT NULL,
    total_responses INTEGER DEFAULT 0,
    correct_responses INTEGER DEFAULT 0,
    difficulty_calculated REAL, -- p-value based on actual responses
    discrimination_calculated REAL, -- discrimination index
    last_calculated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (question_id) REFERENCES questions(id)
);

-- Track option-level analytics for multiple choice questions
CREATE TABLE option_analytics (
    id TEXT PRIMARY KEY,
    question_id TEXT NOT NULL,
    option_letter TEXT NOT NULL,
    selection_count INTEGER DEFAULT 0,
    last_updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (question_id) REFERENCES questions(id)
);

-- Session-level analytics and insights
CREATE TABLE session_analytics (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    total_participants INTEGER DEFAULT 0,
    completion_rate REAL DEFAULT 0.0,
    average_score REAL DEFAULT 0.0,
    average_time_seconds INTEGER DEFAULT 0,
    most_difficult_question_id TEXT,
    most_common_misconception_id TEXT,
    calculated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES sessions(id),
    FOREIGN KEY (most_difficult_question_id) REFERENCES questions(id),
    FOREIGN KEY (most_common_misconception_id) REFERENCES misconceptions(id)
);

-- User activity logging for platform analytics
CREATE TABLE user_activity_logs (
    id TEXT PRIMARY KEY,
    user_id TEXT,
    action TEXT NOT NULL, -- 'login', 'create_assessment', 'start_session', etc.
    resource_type TEXT, -- 'assessment', 'session', 'question', etc.
    resource_id TEXT,
    details TEXT, -- JSON string for additional context
    ip_address TEXT,
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- System-wide usage analytics
CREATE TABLE platform_analytics (
    id TEXT PRIMARY KEY,
    metric_name TEXT NOT NULL,
    metric_value REAL NOT NULL,
    metric_type TEXT CHECK (metric_type IN ('count', 'percentage', 'average', 'duration')) NOT NULL,
    period_start DATETIME NOT NULL,
    period_end DATETIME NOT NULL,
    calculated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- AI-generated content tracking
CREATE TABLE ai_generation_logs (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    content_type TEXT CHECK (content_type IN ('question', 'assessment', 'explanation')) NOT NULL,
    prompt_text TEXT NOT NULL,
    generated_content TEXT NOT NULL,
    model_used TEXT DEFAULT 'viable-3',
    generation_time_ms INTEGER,
    quality_score REAL, -- if rated by user
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Create indexes for performance
CREATE INDEX idx_users_firebase_uid ON users(firebase_uid);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_anonymous_students_firebase_uid ON anonymous_students(firebase_uid);
CREATE INDEX idx_assessments_created_by ON assessments(created_by);
CREATE INDEX idx_assessments_subject_grade ON assessments(subject, grade_level);
CREATE INDEX idx_questions_assessment_id ON questions(assessment_id);
CREATE INDEX idx_questions_misconception_id ON questions(misconception_id);
CREATE INDEX idx_sessions_code ON sessions(session_code);
CREATE INDEX idx_sessions_created_by ON sessions(created_by);
CREATE INDEX idx_session_participants_session_id ON session_participants(session_id);
CREATE INDEX idx_student_responses_session_id ON student_responses(session_id);
CREATE INDEX idx_student_responses_question_id ON student_responses(question_id);
CREATE INDEX idx_user_activity_logs_user_id ON user_activity_logs(user_id);
CREATE INDEX idx_user_activity_logs_action ON user_activity_logs(action);
CREATE INDEX idx_ai_generation_logs_user_id ON ai_generation_logs(user_id);