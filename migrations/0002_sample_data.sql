-- Migration: 0002_sample_data.sql
-- Insert sample standards, misconceptions, and seed data

-- Sample teaching standards (Common Core Math examples)
INSERT INTO standards (id, name, description, subject, grade_level, standard_code) VALUES
('std_001', 'Operations and Algebraic Thinking', 'Understand addition as putting together and subtraction as taking apart', 'Mathematics', 'K', 'CCSS.MATH.CONTENT.K.OA'),
('std_002', 'Number and Operations in Base Ten', 'Work with numbers 11-19 to gain foundations for place value', 'Mathematics', 'K', 'CCSS.MATH.CONTENT.K.NBT'),
('std_003', 'Operations and Algebraic Thinking', 'Represent and solve problems involving addition and subtraction', 'Mathematics', '1', 'CCSS.MATH.CONTENT.1.OA.A'),
('std_004', 'Number and Operations in Base Ten', 'Understand place value', 'Mathematics', '1', 'CCSS.MATH.CONTENT.1.NBT.B'),
('std_005', 'Operations and Algebraic Thinking', 'Add and subtract within 20', 'Mathematics', '2', 'CCSS.MATH.CONTENT.2.OA.B'),
('std_006', 'Number and Operations in Base Ten', 'Understand place value within 100', 'Mathematics', '2', 'CCSS.MATH.CONTENT.2.NBT.A'),
('std_007', 'Operations and Algebraic Thinking', 'Represent and solve problems involving multiplication and division', 'Mathematics', '3', 'CCSS.MATH.CONTENT.3.OA.A'),
('std_008', 'Number and Operations - Fractions', 'Develop understanding of fractions as numbers', 'Mathematics', '3', 'CCSS.MATH.CONTENT.3.NF'),
('std_009', 'Operations and Algebraic Thinking', 'Generate and analyze patterns', 'Mathematics', '4', 'CCSS.MATH.CONTENT.4.OA.C'),
('std_010', 'Number and Operations - Fractions', 'Build fractions from unit fractions', 'Mathematics', '4', 'CCSS.MATH.CONTENT.4.NF.B');

-- Sample common misconceptions
INSERT INTO misconceptions (id, title, description, standard_id, subject, grade_level, category, frequency_score) VALUES
('misc_001', 'Addition/Subtraction Direction Confusion', 'Students think subtraction always makes numbers smaller and addition always makes them bigger, even with negative numbers', 'std_001', 'Mathematics', 'K', 'conceptual', 0.7),
('misc_002', 'Place Value Confusion', 'Students think the position of digits doesn''t matter (e.g., 42 and 24 are the same)', 'std_002', 'Mathematics', 'K', 'conceptual', 0.6),
('misc_003', 'Counting vs. Calculating', 'Students rely only on counting instead of using addition/subtraction strategies', 'std_003', 'Mathematics', '1', 'procedural', 0.5),
('misc_004', 'Tens and Ones Confusion', 'Students confuse the value of digits in tens and ones places', 'std_004', 'Mathematics', '1', 'conceptual', 0.8),
('misc_005', 'Borrowing/Regrouping Errors', 'Students make errors when regrouping in subtraction (borrowing)', 'std_005', 'Mathematics', '2', 'procedural', 0.75),
('misc_006', 'Two-Digit Place Value', 'Students misunderstand that 45 means 4 tens and 5 ones', 'std_006', 'Mathematics', '2', 'conceptual', 0.65),
('misc_007', 'Multiplication as Repeated Addition', 'Students don''t understand multiplication as groups of equal amounts', 'std_007', 'Mathematics', '3', 'conceptual', 0.7),
('misc_008', 'Fraction Misconceptions', 'Students think larger denominators mean larger fractions', 'std_008', 'Mathematics', '3', 'conceptual', 0.85),
('misc_009', 'Pattern Recognition', 'Students focus on superficial features rather than mathematical relationships in patterns', 'std_009', 'Mathematics', '4', 'conceptual', 0.6),
('misc_010', 'Fraction Addition Errors', 'Students add numerators and denominators separately', 'std_010', 'Mathematics', '4', 'procedural', 0.9);

-- Sample assessment for demonstration
INSERT INTO assessments (id, title, description, subject, grade_level, created_by, is_public, target_difficulty) VALUES
('assess_demo', 'Demo: 3rd Grade Fractions', 'Sample assessment showing platform capabilities for fraction understanding', 'Mathematics', '3', 'demo_teacher', TRUE, 0.6);

-- Sample questions for the demo assessment
INSERT INTO questions (id, assessment_id, question_text, question_type, correct_answer, explanation, standard_id, misconception_id, created_by, difficulty) VALUES
('q_001', 'assess_demo', 'Which fraction represents the largest amount?', 'multiple_choice', 'A', 'When fractions have the same numerator, the one with the smaller denominator is larger because the pieces are bigger.', 'std_008', 'misc_008', 'demo_teacher', 0.4),
('q_002', 'assess_demo', 'If you eat 2/8 of a pizza and your friend eats 1/4 of the same pizza, who ate more?', 'multiple_choice', 'C', 'Both amounts are equal. 2/8 = 1/4 when reduced to lowest terms.', 'std_008', 'misc_008', 'demo_teacher', 0.6),
('q_003', 'assess_demo', 'What is 1/3 + 1/3?', 'multiple_choice', 'B', 'When adding fractions with the same denominator, add the numerators and keep the denominator the same.', 'std_008', 'misc_010', 'demo_teacher', 0.7);

-- Sample multiple choice options
INSERT INTO question_options (id, question_id, option_letter, option_text, is_correct, misconception_id) VALUES
-- Question 1 options
('opt_001_a', 'q_001', 'A', '3/4', TRUE, NULL),
('opt_001_b', 'q_001', 'B', '3/8', FALSE, 'misc_008'),
('opt_001_c', 'q_001', 'C', '3/12', FALSE, 'misc_008'),
('opt_001_d', 'q_001', 'D', 'They are all equal', FALSE, 'misc_008'),

-- Question 2 options  
('opt_002_a', 'q_002', 'A', 'You ate more', FALSE, 'misc_008'),
('opt_002_b', 'q_002', 'B', 'Your friend ate more', FALSE, 'misc_008'),
('opt_002_c', 'q_002', 'C', 'You both ate the same amount', TRUE, NULL),
('opt_002_d', 'q_002', 'D', 'Cannot be determined', FALSE, NULL),

-- Question 3 options
('opt_003_a', 'q_003', 'A', '1/6', FALSE, 'misc_010'),
('opt_003_b', 'q_003', 'B', '2/3', TRUE, NULL),
('opt_003_c', 'q_003', 'C', '2/6', FALSE, 'misc_010'),
('opt_003_d', 'q_003', 'D', '1/3', FALSE, NULL);

-- Initialize analytics tables with zero values for the demo questions
INSERT INTO question_analytics (id, question_id, total_responses, correct_responses, difficulty_calculated) VALUES
('qa_001', 'q_001', 0, 0, 0.4),
('qa_002', 'q_002', 0, 0, 0.6),
('qa_003', 'q_003', 0, 0, 0.7);

INSERT INTO option_analytics (id, question_id, option_letter, selection_count) VALUES
('oa_001_a', 'q_001', 'A', 0),
('oa_001_b', 'q_001', 'B', 0),
('oa_001_c', 'q_001', 'C', 0),
('oa_001_d', 'q_001', 'D', 0),
('oa_002_a', 'q_002', 'A', 0),
('oa_002_b', 'q_002', 'B', 0),
('oa_002_c', 'q_002', 'C', 0),
('oa_002_d', 'q_002', 'D', 0),
('oa_003_a', 'q_003', 'A', 0),
('oa_003_b', 'q_003', 'B', 0),
('oa_003_c', 'q_003', 'C', 0),
('oa_003_d', 'q_003', 'D', 0);