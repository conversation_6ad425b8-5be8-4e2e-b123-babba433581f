import { verifyFirebaseToken } from '../utils/verifyFirebaseToken'
import { getDB } from '../utils/db'

const PROTECTED_ROUTES = ['/api/assessments', '/api/questions', '/api/sessions', '/api/responses', '/api/ai', '/api/auth/sync']

export default defineEventHandler(async (event) => {
  const path = getRequestURL(event).pathname
  const isProtected = PROTECTED_ROUTES.some((route) => path.startsWith(route))
  if (!isProtected) return

  const token = getHeader(event, 'authorization')?.split(' ')[1]
  if (!token) {
    throw createError({ statusCode: 401, statusMessage: 'Unauthorized: No token provided' })
  }

  const config = useRuntimeConfig()
  const projectId = (config as any).public.firebase.projectId as string

  try {
    const decoded = await verifyFirebaseToken(token, projectId)
    const uid = decoded.user_id || decoded.sub

    // Allow sync endpoint to proceed even if user is not yet in DB
    if (path.startsWith('/api/auth/sync')) {
      ;(event as any).context.auth = { uid, email: decoded.email, name: decoded.name }
      return
    }

    const db = getDB(event)
    const { results } = await db
      .prepare('SELECT id, email, display_name, role FROM users WHERE id = ?')
      .bind(uid)
      .all()

    if (!results || results.length === 0) {
      throw createError({ statusCode: 403, statusMessage: 'Forbidden: User not found in database' })
    }

    ;(event as any).context.user = results[0]
  } catch (err) {
    throw createError({ statusCode: 401, statusMessage: 'Unauthorized: Invalid token' })
  }
})


