import { createRemoteJWKSet, jwtVerify } from 'jose'

const JWKS = createRemoteJWKSet(new URL('https://www.googleapis.com/service_accounts/v1/jwk/<EMAIL>'))

export async function verifyFirebaseToken(idToken: string, projectId: string) {
  const issuer = `https://securetoken.google.com/${projectId}`
  const { payload } = await jwtVerify(idToken, JWKS, {
    issuer,
    audience: projectId,
  })
  return payload as unknown as {
    aud: string
    auth_time: number
    exp: number
    iat: number
    iss: string
    sub: string
    user_id: string
    email?: string
    name?: string
    uid?: string
  }
}


