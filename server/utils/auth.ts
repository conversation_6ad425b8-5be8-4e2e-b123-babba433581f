import { initializeApp, cert, type App } from 'firebase-admin/app';
import { getAuth, type Auth } from 'firebase-admin/auth';

let adminApp: App | null = null;
let adminAuth: Auth | null = null;

function getFirebaseAdmin() {
  if (!adminApp) {
    // Initialize Firebase Admin with the same project ID as client config
    adminApp = initializeApp({
      projectId: 'viablelearning'
    });
    adminAuth = getAuth(adminApp);
  }
  return adminAuth;
}

export async function verifyFirebaseToken(token: string) {
  try {
    const auth = getFirebaseAdmin();
    if (!auth) {
      throw new Error('Firebase admin not initialized');
    }
    
    const decodedToken = await auth.verifyIdToken(token);
    return decodedToken;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

export async function requireAuth(event: any) {
  const authHeader = getHeader(event, 'authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized - Missing or invalid authorization header'
    });
  }
  
  const token = authHeader.substring(7);
  const decodedToken = await verifyFirebaseToken(token);
  
  if (!decodedToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized - Invalid token'
    });
  }
  
  return decodedToken;
}