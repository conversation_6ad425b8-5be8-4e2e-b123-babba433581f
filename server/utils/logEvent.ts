import { getDB } from './db'

export async function logEvent(event: any, eventType: string, payload: unknown, sessionId?: string) {
  try {
    const db = getDB(event)
    const ip = getHeader(event, 'cf-connecting-ip') || getHeader(event, 'x-forwarded-for') || ''
    const userAgent = getHeader(event, 'user-agent') || ''
    const userId = (event as any)?.context?.user?.id || (event as any)?.context?.auth?.uid || null

    await db
      .prepare(
        `INSERT INTO event_logs (user_id, session_id, event_type, payload, ip_address, user_agent)
         VALUES (?, ?, ?, ?, ?, ?)`
      )
      .bind(userId, sessionId ?? null, eventType, JSON.stringify(payload ?? {}), ip, userAgent)
      .run()
  } catch (_e) {
    // Do not throw from logging
  }
}


