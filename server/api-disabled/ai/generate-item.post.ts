import { requireAuth } from './utils/auth';
import { getDB, logEvent } from './utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const body = await readBody(event);
    
    if (!body.prompt || !body.prompt.trim()) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Prompt is required'
      });
    }
    
    // Construct a detailed prompt for the AI
    const systemPrompt = `You are an educational assessment expert. Create a multiple-choice question based on the following request. 

Requirements:
- Create exactly 4 answer options (A, B, C, D)
- One option should be the correct answer
- The other options should be plausible distractors that target common misconceptions
- Include a brief rationale explaining why the correct answer is right and what misconceptions the distractors address
- Format your response as JSON with this exact structure:

{
  "content": {
    "question": "The question text here",
    "options": [
      {"text": "Option A text"},
      {"text": "Option B text"},
      {"text": "Option C text"},
      {"text": "Option D text"}
    ],
    "answer": 0
  },
  "rationale": "Explanation of the correct answer and common misconceptions addressed by distractors"
}

User Request: ${body.prompt}`;
    
    // Make request to lab's LLM API
    const response = await fetch('https://api.viablelab.org/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'viable-3',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      })
    });
    
    if (!response.ok) {
      throw createError({
        statusCode: 500,
        statusMessage: 'AI generation failed'
      });
    }
    
    const aiResponse = await response.json();
    const generatedContent = aiResponse.choices?.[0]?.message?.content;
    
    if (!generatedContent) {
      throw createError({
        statusCode: 500,
        statusMessage: 'No content generated'
      });
    }
    
    // Parse the JSON response
    let parsedContent;
    try {
      parsedContent = JSON.parse(generatedContent);
    } catch (parseError) {
      // If JSON parsing fails, try to extract content manually
      console.error('Failed to parse AI response as JSON:', parseError);
      throw createError({
        statusCode: 500,
        statusMessage: 'Invalid AI response format'
      });
    }
    
    // Validate the structure
    if (!parsedContent.content?.question || 
        !Array.isArray(parsedContent.content?.options) || 
        parsedContent.content?.options.length !== 4 ||
        typeof parsedContent.content?.answer !== 'number') {
      throw createError({
        statusCode: 500,
        statusMessage: 'Invalid generated content structure'
      });
    }
    
    // Log the AI generation event
    if (db) {
      await logEvent(db, decodedToken.uid, 'AI_ITEM_GENERATED', {
        prompt: body.prompt,
        generated_question: parsedContent.content.question
      });
    }
    
    return {
      item: parsedContent
    };
    
  } catch (error) {
    console.error('AI generation error:', error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'AI generation service temporarily unavailable'
    });
  }
});