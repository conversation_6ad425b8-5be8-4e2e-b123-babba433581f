import { requireAuth } from './utils/auth';
import { getDB, logEvent } from './utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    // Check if user exists, if not create them
    const existingUser = await db.prepare(`
      SELECT * FROM users WHERE id = ?
    `).bind(decodedToken.uid).first();
    
    if (!existingUser) {
      // Create new user
      await db.prepare(`
        INSERT INTO users (id, email, display_name)
        VALUES (?, ?, ?)
      `).bind(
        decodedToken.uid,
        decodedToken.email,
        decodedToken.name || decodedToken.email
      ).run();
      
      await logEvent(db, decodedToken.uid, 'USER_CREATED', {
        email: decodedToken.email,
        name: decodedToken.name
      });
    } else {
      // Update existing user info if needed
      if (existingUser.display_name !== (decodedToken.name || decodedToken.email)) {
        await db.prepare(`
          UPDATE users SET display_name = ? WHERE id = ?
        `).bind(decodedToken.name || decodedToken.email, decodedToken.uid).run();
      }
    }
    
    await logEvent(db, decodedToken.uid, 'USER_LOGIN');
    
    return {
      status: 'success',
      user: {
        id: decodedToken.uid,
        email: decodedToken.email,
        displayName: decodedToken.name || decodedToken.email
      }
    };
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
});