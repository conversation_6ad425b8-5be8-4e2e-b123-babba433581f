import { requireAuth } from './utils/auth';
import { getDB, logEvent } from './utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const assessmentId = getRouterParam(event, 'id');
    const body = await readBody(event);
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!assessmentId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Assessment ID is required'
      });
    }
    
    if (!body.title || !body.title.trim()) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Assessment title is required'
      });
    }
    
    // Verify ownership
    const assessment = await db.prepare(`
      SELECT * FROM assessments WHERE id = ? AND user_id = ?
    `).bind(assessmentId, decodedToken.uid).first();
    
    if (!assessment) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Assessment not found'
      });
    }
    
    // Update the assessment
    await db.prepare(`
      UPDATE assessments 
      SET title = ?, description = ?, status = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).bind(
      body.title.trim(),
      body.description || null,
      body.status || assessment.status,
      assessmentId
    ).run();
    
    // Delete existing items
    await db.prepare(`
      DELETE FROM items WHERE assessment_id = ?
    `).bind(assessmentId).run();
    
    // Add new items
    if (body.items && Array.isArray(body.items)) {
      for (let i = 0; i < body.items.length; i++) {
        const item = body.items[i];
        
        if (!item.content || !item.content.question) {
          continue; // Skip invalid items
        }
        
        await db.prepare(`
          INSERT INTO items (assessment_id, item_order, item_type, content, psychometric_data)
          VALUES (?, ?, ?, ?, ?)
        `).bind(
          assessmentId,
          item.item_order !== undefined ? item.item_order : i,
          item.item_type || 'multiple_choice',
          JSON.stringify(item.content),
          item.psychometric_data ? JSON.stringify(item.psychometric_data) : null
        ).run();
      }
    }
    
    await logEvent(db, decodedToken.uid, 'ASSESSMENT_UPDATED', {
      assessment_id: assessmentId,
      title: body.title,
      item_count: body.items?.length || 0
    });
    
    return {
      status: 'success',
      updated_at: new Date().toISOString()
    };
    
  } catch (error) {
    console.error('Update assessment error:', error);
    throw error;
  }
});