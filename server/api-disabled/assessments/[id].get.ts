import { requireAuth } from './utils/auth';
import { getDB } from './utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const assessmentId = getRouterParam(event, 'id');
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!assessmentId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Assessment ID is required'
      });
    }
    
    // Get the assessment
    const assessment = await db.prepare(`
      SELECT * FROM assessments WHERE id = ? AND user_id = ?
    `).bind(assessmentId, decodedToken.uid).first();
    
    if (!assessment) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Assessment not found'
      });
    }
    
    // Get all items for this assessment
    const items = await db.prepare(`
      SELECT * FROM items WHERE assessment_id = ? ORDER BY item_order ASC
    `).bind(assessmentId).all();
    
    // Parse content JSON for each item
    const parsedItems = (items.results || []).map((item: any) => ({
      ...item,
      content: JSON.parse(item.content),
      psychometric_data: item.psychometric_data ? JSON.parse(item.psychometric_data) : null
    }));
    
    return {
      ...assessment,
      items: parsedItems
    };
    
  } catch (error) {
    console.error('Get assessment error:', error);
    throw error;
  }
});