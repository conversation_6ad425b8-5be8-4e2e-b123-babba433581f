import { requireAuth } from './utils/auth';
import { getDB, logEvent } from './utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const assessmentId = getRouterParam(event, 'id');
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!assessmentId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Assessment ID is required'
      });
    }
    
    // Verify ownership
    const assessment = await db.prepare(`
      SELECT * FROM assessments WHERE id = ? AND user_id = ?
    `).bind(assessmentId, decodedToken.uid).first();
    
    if (!assessment) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Assessment not found'
      });
    }
    
    // Delete the assessment (items will be deleted via CASCADE)
    await db.prepare(`
      DELETE FROM assessments WHERE id = ?
    `).bind(assessmentId).run();
    
    await logEvent(db, decodedToken.uid, 'ASSESSMENT_DELETED', {
      assessment_id: assessmentId,
      title: assessment.title
    });
    
    return {
      status: 'success'
    };
    
  } catch (error) {
    console.error('Delete assessment error:', error);
    throw error;
  }
});