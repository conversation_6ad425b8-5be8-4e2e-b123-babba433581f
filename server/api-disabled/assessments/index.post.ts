import { requireAuth } from './utils/auth';
import { getDB, logEvent } from './utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const body = await readBody(event);
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!body.title || !body.title.trim()) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Assessment title is required'
      });
    }
    
    // Create the assessment
    const assessmentResult = await db.prepare(`
      INSERT INTO assessments (user_id, title, description, status)
      VALUES (?, ?, ?, ?)
    `).bind(
      decodedToken.uid,
      body.title.trim(),
      body.description || null,
      body.status || 'draft'
    ).run();
    
    const assessmentId = assessmentResult.meta.last_row_id;
    
    // Add items if provided
    if (body.items && Array.isArray(body.items)) {
      for (let i = 0; i < body.items.length; i++) {
        const item = body.items[i];
        
        if (!item.content || !item.content.question) {
          continue; // Skip invalid items
        }
        
        await db.prepare(`
          INSERT INTO items (assessment_id, item_order, item_type, content, psychometric_data)
          VALUES (?, ?, ?, ?, ?)
        `).bind(
          assessmentId,
          item.item_order !== undefined ? item.item_order : i,
          item.item_type || 'multiple_choice',
          JSON.stringify(item.content),
          item.psychometric_data ? JSON.stringify(item.psychometric_data) : null
        ).run();
      }
    }
    
    // Update the assessment's updated_at timestamp
    await db.prepare(`
      UPDATE assessments SET updated_at = CURRENT_TIMESTAMP WHERE id = ?
    `).bind(assessmentId).run();
    
    await logEvent(db, decodedToken.uid, 'ASSESSMENT_CREATED', {
      assessment_id: assessmentId,
      title: body.title,
      item_count: body.items?.length || 0
    });
    
    return {
      id: assessmentId,
      title: body.title,
      status: body.status || 'draft'
    };
    
  } catch (error) {
    console.error('Create assessment error:', error);
    throw error;
  }
});