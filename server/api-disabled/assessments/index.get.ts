import { requireAuth } from './utils/auth';
import { getDB } from './utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    // Get all assessments for the user with item counts
    const assessments = await db.prepare(`
      SELECT 
        a.*,
        COUNT(i.id) as item_count
      FROM assessments a
      LEFT JOIN items i ON a.id = i.assessment_id
      WHERE a.user_id = ?
      GROUP BY a.id
      ORDER BY a.updated_at DESC
    `).bind(decodedToken.uid).all();
    
    return assessments.results || [];
    
  } catch (error) {
    console.error('Get assessments error:', error);
    throw error;
  }
});