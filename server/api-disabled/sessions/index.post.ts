import { requireAuth } from './utils/auth';
import { getDB, generateJoinCode, logEvent } from './utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const body = await readBody(event);
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!body.assessment_id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Assessment ID is required'
      });
    }
    
    // Verify the assessment exists and belongs to the user
    const assessment = await db.prepare(`
      SELECT * FROM assessments WHERE id = ? AND user_id = ?
    `).bind(body.assessment_id, decodedToken.uid).first();
    
    if (!assessment) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Assessment not found'
      });
    }
    
    // Generate a unique join code
    let joinCode;
    let attempts = 0;
    do {
      joinCode = generateJoinCode();
      const existing = await db.prepare(`
        SELECT id FROM sessions WHERE join_code = ?
      `).bind(joinCode).first();
      
      if (!existing) break;
      
      attempts++;
      if (attempts > 10) {
        throw createError({
          statusCode: 500,
          statusMessage: 'Failed to generate unique join code'
        });
      }
    } while (true);
    
    // Create the session
    const sessionResult = await db.prepare(`
      INSERT INTO sessions (assessment_id, join_code, status, expires_at)
      VALUES (?, ?, 'waiting', datetime('now', '+24 hours'))
    `).bind(body.assessment_id, joinCode).run();
    
    const sessionId = sessionResult.meta.last_row_id;
    
    await logEvent(db, decodedToken.uid, 'SESSION_CREATED', {
      session_id: sessionId,
      assessment_id: body.assessment_id,
      join_code: joinCode
    });
    
    return {
      session_id: sessionId,
      join_code: joinCode,
      status: 'waiting'
    };
    
  } catch (error) {
    console.error('Create session error:', error);
    throw error;
  }
});