import { getDB, logEvent } from '../utils/db';

export default defineEventHandler(async (event) => {
  try {
    const db = getDB(event);
    const joinCode = getRouterParam(event, 'code');
    const body = await readBody(event);
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!joinCode || !body.nickname) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Join code and nickname are required'
      });
    }
    
    // Find and validate the session
    const session = await db.prepare(`
      SELECT 
        s.*,
        a.title as assessment_title
      FROM sessions s
      JOIN assessments a ON s.assessment_id = a.id
      WHERE s.join_code = ? 
        AND s.expires_at > datetime('now')
        AND s.status IN ('waiting', 'active')
    `).bind(joinCode.toUpperCase()).first();
    
    if (!session) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Session not found or has expired'
      });
    }
    
    // Check if participant already exists with this nickname
    const existingParticipant = await db.prepare(`
      SELECT id FROM participants WHERE session_id = ? AND nickname = ?
    `).bind(session.id, body.nickname.trim()).first();
    
    if (existingParticipant) {
      throw createError({
        statusCode: 400,
        statusMessage: 'A participant with this nickname already exists in this session'
      });
    }
    
    // Create participant
    const participantResult = await db.prepare(`
      INSERT INTO participants (session_id, nickname)
      VALUES (?, ?)
    `).bind(session.id, body.nickname.trim()).run();
    
    const participantId = participantResult.meta.last_row_id;
    
    // Get assessment items
    const items = await db.prepare(`
      SELECT id, item_order, content FROM items 
      WHERE assessment_id = ? 
      ORDER BY item_order ASC
    `).bind(session.assessment_id).all();
    
    const parsedItems = (items.results || []).map((item: any) => ({
      id: item.id,
      item_order: item.item_order,
      content: JSON.parse(item.content)
    }));
    
    await logEvent(db, null, 'PARTICIPANT_JOINED', {
      session_id: session.id,
      participant_id: participantId,
      nickname: body.nickname.trim()
    });
    
    return {
      participant_id: participantId,
      session_status: session.status,
      assessment_items: parsedItems
    };
    
  } catch (error) {
    console.error('Join session error:', error);
    throw error;
  }
});