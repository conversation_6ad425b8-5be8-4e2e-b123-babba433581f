import { getDB } from '../utils/db';

export default defineEventHandler(async (event) => {
  try {
    const db = getDB(event);
    const joinCode = getRouterParam(event, 'code');
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!joinCode) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Join code is required'
      });
    }
    
    // Find the session
    const session = await db.prepare(`
      SELECT 
        s.*,
        a.title as assessment_title,
        a.description
      FROM sessions s
      JOIN assessments a ON s.assessment_id = a.id
      WHERE s.join_code = ? 
        AND s.expires_at > datetime('now')
        AND s.status IN ('waiting', 'active')
    `).bind(joinCode.toUpperCase()).first();
    
    if (!session) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Session not found or has expired'
      });
    }
    
    return {
      session_id: session.id,
      assessment_title: session.assessment_title,
      description: session.description,
      status: session.status
    };
    
  } catch (error) {
    console.error('Join code validation error:', error);
    throw error;
  }
});