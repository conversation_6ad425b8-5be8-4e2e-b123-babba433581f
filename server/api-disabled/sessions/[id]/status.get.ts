import { getDB } from '../utils/db';

export default defineEventHandler(async (event) => {
  try {
    const db = getDB(event);
    const sessionId = getRouterParam(event, 'id');
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!sessionId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session ID is required'
      });
    }
    
    // Get session status
    const session = await db.prepare(`
      SELECT status FROM sessions WHERE id = ?
    `).bind(sessionId).first();
    
    if (!session) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Session not found'
      });
    }
    
    return {
      status: session.status
    };
    
  } catch (error) {
    console.error('Get session status error:', error);
    throw error;
  }
});