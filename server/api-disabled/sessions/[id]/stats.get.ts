import { requireAuth } from '../utils/auth';
import { getDB } from '../utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const sessionId = getRouterParam(event, 'id');
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!sessionId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session ID is required'
      });
    }
    
    // Verify ownership
    const session = await db.prepare(`
      SELECT s.*, a.user_id
      FROM sessions s
      JOIN assessments a ON s.assessment_id = a.id
      WHERE s.id = ?
    `).bind(sessionId).first();
    
    if (!session || session.user_id !== decodedToken.uid) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Session not found'
      });
    }
    
    // Get response count
    const responseStats = await db.prepare(`
      SELECT COUNT(*) as total_responses
      FROM responses r
      JOIN participants p ON r.participant_id = p.id
      WHERE p.session_id = ?
    `).bind(sessionId).first();
    
    return {
      total_responses: responseStats?.total_responses || 0
    };
    
  } catch (error) {
    console.error('Get session stats error:', error);
    throw error;
  }
});