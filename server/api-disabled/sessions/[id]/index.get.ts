import { requireAuth } from '../utils/auth';
import { getDB } from '../utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const sessionId = getRouterParam(event, 'id');
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!sessionId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session ID is required'
      });
    }
    
    // Get session with assessment info
    const session = await db.prepare(`
      SELECT 
        s.*,
        a.title as assessment_title,
        a.description,
        a.user_id,
        COUNT(i.id) as total_questions
      FROM sessions s
      JOIN assessments a ON s.assessment_id = a.id
      LEFT JOIN items i ON a.id = i.assessment_id
      WHERE s.id = ?
      GROUP BY s.id
    `).bind(sessionId).first();
    
    if (!session) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Session not found'
      });
    }
    
    // Verify user owns this session
    if (session.user_id !== decodedToken.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access denied'
      });
    }
    
    return session;
    
  } catch (error) {
    console.error('Get session error:', error);
    throw error;
  }
});