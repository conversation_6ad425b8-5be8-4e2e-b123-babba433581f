import { requireAuth } from '../utils/auth';
import { getDB, logEvent } from '../utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const sessionId = getRouterParam(event, 'id');
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!sessionId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session ID is required'
      });
    }
    
    // Verify ownership
    const session = await db.prepare(`
      SELECT s.*, a.user_id
      FROM sessions s
      JOIN assessments a ON s.assessment_id = a.id
      WHERE s.id = ?
    `).bind(sessionId).first();
    
    if (!session || session.user_id !== decodedToken.uid) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Session not found'
      });
    }
    
    if (session.status !== 'waiting') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session cannot be started from current status'
      });
    }
    
    // Update session status
    await db.prepare(`
      UPDATE sessions SET status = 'active' WHERE id = ?
    `).bind(sessionId).run();
    
    await logEvent(db, decodedToken.uid, 'SESSION_STARTED', {
      session_id: sessionId
    });
    
    return {
      status: 'success'
    };
    
  } catch (error) {
    console.error('Start session error:', error);
    throw error;
  }
});