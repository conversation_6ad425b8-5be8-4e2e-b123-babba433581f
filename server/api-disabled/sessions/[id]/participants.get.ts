import { requireAuth } from '../utils/auth';
import { getDB } from '../utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const sessionId = getRouterParam(event, 'id');
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!sessionId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session ID is required'
      });
    }
    
    // Verify user owns this session
    const session = await db.prepare(`
      SELECT a.user_id
      FROM sessions s
      JOIN assessments a ON s.assessment_id = a.id
      WHERE s.id = ?
    `).bind(sessionId).first();
    
    if (!session || session.user_id !== decodedToken.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Access denied'
      });
    }
    
    // Get participants
    const participants = await db.prepare(`
      SELECT * FROM participants 
      WHERE session_id = ?
      ORDER BY created_at ASC
    `).bind(sessionId).all();
    
    return participants.results || [];
    
  } catch (error) {
    console.error('Get participants error:', error);
    throw error;
  }
});