import { requireAuth } from './utils/auth';
import { getDB } from './utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    // Get total sessions
    const sessionsResult = await db.prepare(`
      SELECT COUNT(*) as count
      FROM sessions s
      JOIN assessments a ON s.assessment_id = a.id
      WHERE a.user_id = ?
    `).bind(decodedToken.uid).first();
    
    // Get total participants across all sessions
    const participantsResult = await db.prepare(`
      SELECT COUNT(DISTINCT p.id) as count
      FROM participants p
      JOIN sessions s ON p.session_id = s.id
      JOIN assessments a ON s.assessment_id = a.id
      WHERE a.user_id = ?
    `).bind(decodedToken.uid).first();
    
    // Calculate average score across all completed sessions
    const avgScoreResult = await db.prepare(`
      SELECT 
        AVG(
          CAST(SUM(CASE WHEN r.is_correct THEN 1 ELSE 0 END) AS FLOAT) / 
          COUNT(r.id) * 100
        ) as avg_score
      FROM participants p
      JOIN sessions s ON p.session_id = s.id
      JOIN assessments a ON s.assessment_id = a.id
      JOIN responses r ON p.id = r.participant_id
      WHERE a.user_id = ? AND s.status = 'finished'
      GROUP BY p.id
    `).bind(decodedToken.uid).first();
    
    return {
      totalSessions: sessionsResult?.count || 0,
      totalParticipants: participantsResult?.count || 0,
      avgScore: Math.round(avgScoreResult?.avg_score || 0)
    };
    
  } catch (error) {
    console.error('Get dashboard stats error:', error);
    return {
      totalSessions: 0,
      totalParticipants: 0,
      avgScore: 0
    };
  }
});