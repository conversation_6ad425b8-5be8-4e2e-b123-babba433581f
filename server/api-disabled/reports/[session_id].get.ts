import { requireAuth } from './utils/auth';
import { getDB } from './utils/db';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);
    const sessionId = getRouterParam(event, 'session_id');
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!sessionId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session ID is required'
      });
    }
    
    // Verify ownership and session exists
    const session = await db.prepare(`
      SELECT 
        s.*,
        a.title as assessment_title,
        a.description,
        a.user_id
      FROM sessions s
      JOIN assessments a ON s.assessment_id = a.id
      WHERE s.id = ?
    `).bind(sessionId).first();
    
    if (!session || session.user_id !== decodedToken.uid) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Report not found'
      });
    }
    
    if (session.status !== 'finished') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session is not yet completed'
      });
    }
    
    // Get basic session metrics
    const participants = await db.prepare(`
      SELECT * FROM participants WHERE session_id = ?
    `).bind(sessionId).all();
    
    const totalParticipants = participants.results?.length || 0;
    
    // Get all responses for analysis
    const responses = await db.prepare(`
      SELECT 
        r.*,
        p.nickname,
        i.content as item_content,
        i.item_order
      FROM responses r
      JOIN participants p ON r.participant_id = p.id
      JOIN items i ON r.item_id = i.id
      WHERE p.session_id = ?
      ORDER BY i.item_order, p.nickname
    `).bind(sessionId).all();
    
    const allResponses = responses.results || [];
    
    // Calculate participant results
    const participantResults: any = {};
    let totalCorrect = 0;
    let totalResponses = 0;
    
    for (const response of allResponses) {
      if (!participantResults[response.participant_id]) {
        participantResults[response.participant_id] = {
          id: response.participant_id,
          nickname: response.nickname,
          responses: [],
          correct_answers: 0,
          total_responses: 0,
          score: 0
        };
      }
      
      participantResults[response.participant_id].responses.push(response);
      participantResults[response.participant_id].total_responses++;
      totalResponses++;
      
      if (response.is_correct) {
        participantResults[response.participant_id].correct_answers++;
        totalCorrect++;
      }
    }
    
    // Calculate scores and completion
    const participantList = Object.values(participantResults).map((p: any) => {
      p.score = p.total_responses > 0 ? (p.correct_answers / p.total_responses) * 100 : 0;
      p.completed = p.total_responses > 0; // Simplified completion check
      return p;
    });
    
    const averageScore = totalResponses > 0 ? (totalCorrect / totalResponses) * 100 : 0;
    const scores = participantList.map(p => p.score);
    const highestScore = scores.length > 0 ? Math.max(...scores) : 0;
    const lowestScore = scores.length > 0 ? Math.min(...scores) : 0;
    const completionRate = totalParticipants > 0 ? (participantList.filter(p => p.completed).length / totalParticipants) * 100 : 0;
    
    // Calculate score distribution
    const scoreDistribution = {
      '90-100': 0,
      '80-89': 0,
      '70-79': 0,
      '60-69': 0,
      '50-59': 0,
      '0-49': 0
    };
    
    for (const score of scores) {
      if (score >= 90) scoreDistribution['90-100']++;
      else if (score >= 80) scoreDistribution['80-89']++;
      else if (score >= 70) scoreDistribution['70-79']++;
      else if (score >= 60) scoreDistribution['60-69']++;
      else if (score >= 50) scoreDistribution['50-59']++;
      else scoreDistribution['0-49']++;
    }
    
    // Analyze questions
    const questions = await db.prepare(`
      SELECT * FROM items WHERE assessment_id = ? ORDER BY item_order
    `).bind(session.assessment_id).all();
    
    const questionAnalysis = (questions.results || []).map((item: any) => {
      const itemContent = JSON.parse(item.content);
      const itemResponses = allResponses.filter(r => r.item_id === item.id);
      const totalItemResponses = itemResponses.length;
      const correctResponses = itemResponses.filter(r => r.is_correct).length;
      
      // Analyze option selections
      const optionCounts: any = {};
      itemResponses.forEach(r => {
        const responseData = JSON.parse(r.response_data);
        const answerIndex = responseData.answer_index;
        optionCounts[answerIndex] = (optionCounts[answerIndex] || 0) + 1;
      });
      
      const options = itemContent.options.map((option: any, index: number) => ({
        text: option.text,
        is_correct: index === itemContent.answer,
        count: optionCounts[index] || 0,
        percentage: totalItemResponses > 0 ? ((optionCounts[index] || 0) / totalItemResponses) * 100 : 0
      }));
      
      return {
        id: item.id,
        question_text: itemContent.question,
        options: options,
        total_responses: totalItemResponses,
        correct_responses: correctResponses,
        correct_percentage: totalItemResponses > 0 ? (correctResponses / totalItemResponses) * 100 : 0,
        misconceptions: [] // Could be enhanced with AI analysis
      };
    });
    
    return {
      session_id: sessionId,
      assessment_title: session.assessment_title,
      description: session.description,
      session_date: session.created_at,
      total_participants: totalParticipants,
      total_questions: questions.results?.length || 0,
      average_score: averageScore,
      highest_score: highestScore,
      lowest_score: lowestScore,
      completion_rate: completionRate,
      average_time: 0, // Would need to track timing
      score_distribution: scoreDistribution,
      participant_results: participantList,
      question_analysis: questionAnalysis,
      insights: [] // Could be enhanced with AI-generated insights
    };
    
  } catch (error) {
    console.error('Get report error:', error);
    throw error;
  }
});