// Database utility functions
export function getDB(event: any) {
  return event.context.cloudflare?.env?.DB;
}

export function generateJoinCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export async function logEvent(db: any, userId: string | null, eventType: string, details: any = null) {
  try {
    await db.prepare(`
      INSERT INTO logs (user_id, event_type, details)
      VALUES (?, ?, ?)
    `).bind(userId, eventType, details ? JSON.stringify(details) : null).run();
  } catch (error) {
    console.error('Failed to log event:', error);
  }
}