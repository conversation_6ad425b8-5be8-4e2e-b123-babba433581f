type Session = { id: string; code: string; assessmentId: string; started: boolean; participants: string[] }

const SESSIONS: Record<string, Session> = globalThis.__cp_sessions || (globalThis.__cp_sessions = {})

export default defineEventHandler(async(event)=>{
	const body = await readBody<{ assessmentId: string }>(event)
	const id = crypto.randomUUID()
	const code = Math.random().toString().slice(2,8).toUpperCase()
	SESSIONS[id] = { id, code, assessmentId: body.assessmentId, started: false, participants: [] }
	return { id, code }
})

declare global {
	var __cp_sessions: Record<string, Session> | undefined
}

