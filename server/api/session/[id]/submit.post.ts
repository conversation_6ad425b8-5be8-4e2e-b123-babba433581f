type Session = { id: string; code: string; assessmentId: string; started: boolean; participants: string[] }
const SESSIONS: Record<string, Session> = globalThis.__cp_sessions || (globalThis.__cp_sessions = {})

export default defineEventHandler(async(event)=>{
	const id = getRouterParam(event, 'id') as string
	const body = await readBody<{ idToken: string; answers: Record<string, number> }>(event)
	if(!SESSIONS[id]){ throw createError({ statusCode: 404, statusMessage: 'Not found' }) }
	// In the future, verify body.idToken on server and write to D1/Firestore.
	return { ok: true }
})

declare global { var __cp_sessions: Record<string, Session> | undefined }

