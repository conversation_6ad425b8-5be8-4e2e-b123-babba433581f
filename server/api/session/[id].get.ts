type Session = { id: string; code: string; assessmentId: string; started: boolean; participants: string[] }
const SESSIONS: Record<string, Session> = globalThis.__cp_sessions || (globalThis.__cp_sessions = {})

export default defineEventHandler(async(event)=>{
	const id = getRouterParam(event, 'id') as string
	const s = SESSIONS[id]
	if(!s){ throw createError({ statusCode: 404, statusMessage: 'Not found' }) }
	// Mock questions for now
	const questions = [
		{ id: 'q1', stem: 'What is 3/4 of 20?', choices: ['10','12','15','8'] },
		{ id: 'q2', stem: 'Solve: 2x + 5 = 15', choices: ['x=10','x=5','x=3','x=2'] }
	]
	return { id: s.id, started: s.started, participants: s.participants, questions }
})

declare global { var __cp_sessions: Record<string, Session> | undefined }

