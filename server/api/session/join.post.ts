type Session = { id: string; code: string; assessmentId: string; started: boolean; participants: string[] }
const SESSIONS: Record<string, Session> = globalThis.__cp_sessions || (globalThis.__cp_sessions = {})

export default defineEventHandler(async(event)=>{
	const body = await readBody<{ code: string; nickname: string; idToken: string }>(event)
	const session = Object.values(SESSIONS).find(s=>s.code===body.code)
	if(!session){ throw createError({ statusCode: 404, statusMessage: 'Invalid code' }) }
	if(body.nickname){ session.participants.push(body.nickname) }
	return { sessionId: session.id }
})

declare global { var __cp_sessions: Record<string, Session> | undefined }

