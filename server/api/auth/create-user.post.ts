import { nanoid } from 'nanoid'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { firebaseUid, email, displayName, profilePictureUrl, role = 'teacher' } = body

  if (!firebaseUid || !email) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Firebase UID and email are required'
    })
  }

  try {
    const db = event.context.cloudflare?.env?.DB
    if (!db) {
      throw new Error('Database not available')
    }

    const userId = nanoid()
    const now = new Date().toISOString()

    // Check if user already exists
    const existingUser = await db.prepare(
      'SELECT * FROM users WHERE firebase_uid = ?'
    ).bind(firebaseUid).first()

    if (existingUser) {
      // Update last login
      await db.prepare(
        'UPDATE users SET last_login_at = ?, updated_at = ? WHERE firebase_uid = ?'
      ).bind(now, now, firebaseUid).run()

      return { user: existingUser, created: false }
    }

    // Create new user
    await db.prepare(`
      INSERT INTO users (id, firebase_uid, email, display_name, role, profile_picture_url, created_at, updated_at, last_login_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      userId,
      firebaseUid,
      email,
      displayName || null,
      role,
      profilePictureUrl || null,
      now,
      now,
      now
    ).run()

    // Log user creation
    await db.prepare(`
      INSERT INTO user_activity_logs (id, user_id, action, resource_type, created_at)
      VALUES (?, ?, ?, ?, ?)
    `).bind(
      nanoid(),
      userId,
      'account_created',
      'user',
      now
    ).run()

    const newUser = await db.prepare(
      'SELECT * FROM users WHERE id = ?'
    ).bind(userId).first()

    return { user: newUser, created: true }

  } catch (error) {
    console.error('Error creating user:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create user'
    })
  }
})