import { getDB } from '../../utils/db'

export default defineEventHandler(async (event) => {
  const auth = (event as any).context.auth as { uid: string; email?: string; name?: string } | undefined
  if (!auth?.uid) {
    throw createError({ statusCode: 401, statusMessage: 'Unauthorized' })
  }

  const db = getDB(event)
  await db
    .prepare('INSERT OR IGNORE INTO users (id, email, display_name) VALUES (?, ?, ?)')
    .bind(auth.uid, auth.email ?? null, auth.name ?? null)
    .run()

  const { results } = await db
    .prepare('SELECT id, email, display_name, role FROM users WHERE id = ?')
    .bind(auth.uid)
    .all()

  return results?.[0] ?? null
})


