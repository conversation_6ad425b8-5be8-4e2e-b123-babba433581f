import { nanoid } from 'nanoid'

export default defineEventHandler(async (event) => {
  const body = await readBody(event)
  const { firebaseUid } = body

  if (!firebaseUid) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Firebase UID is required'
    })
  }

  try {
    const db = event.context.cloudflare?.env?.DB
    if (!db) {
      throw new Error('Database not available')
    }

    const studentId = nanoid()
    const now = new Date().toISOString()

    // Check if anonymous student already exists
    const existingStudent = await db.prepare(
      'SELECT * FROM anonymous_students WHERE firebase_uid = ?'
    ).bind(firebaseUid).first()

    if (existingStudent) {
      // Update last seen
      await db.prepare(
        'UPDATE anonymous_students SET last_seen_at = ? WHERE firebase_uid = ?'
      ).bind(now, firebaseUid).run()

      return { student: existingStudent, created: false }
    }

    // Create new anonymous student
    await db.prepare(`
      INSERT INTO anonymous_students (id, firebase_uid, created_at, last_seen_at)
      VALUES (?, ?, ?, ?)
    `).bind(
      studentId,
      firebaseUid,
      now,
      now
    ).run()

    const newStudent = await db.prepare(
      'SELECT * FROM anonymous_students WHERE id = ?'
    ).bind(studentId).first()

    return { student: newStudent, created: true }

  } catch (error) {
    console.error('Error creating anonymous student:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create anonymous student'
    })
  }
})