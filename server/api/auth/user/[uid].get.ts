export default defineEventHandler(async (event) => {
  const firebaseUid = getRouterParam(event, 'uid')

  if (!firebaseUid) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Firebase UID is required'
    })
  }

  try {
    const db = event.context.cloudflare?.env?.DB
    if (!db) {
      throw new Error('Database not available')
    }

    // Try to find regular user first
    const user = await db.prepare(
      'SELECT * FROM users WHERE firebase_uid = ?'
    ).bind(firebaseUid).first()

    if (user) {
      return user
    }

    // Try to find anonymous student
    const student = await db.prepare(
      'SELECT * FROM anonymous_students WHERE firebase_uid = ?'
    ).bind(firebaseUid).first()

    if (student) {
      return { ...student, role: 'student', isAnonymous: true }
    }

    throw createError({
      statusCode: 404,
      statusMessage: 'User not found'
    })

  } catch (error) {
    console.error('Error fetching user:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch user'
    })
  }
})