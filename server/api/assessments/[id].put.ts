import { getDB } from '../../utils/db'

export default defineEventHandler(async (event) => {
  const user = (event as any).context.user
  if (!user) throw createError({ statusCode: 401, statusMessage: 'Unauthorized' })

  const id = getRouterParam(event, 'id')
  const body = await readBody<{ title?: string; description?: string; is_validated?: boolean }>(event)
  const db = getDB(event)

  const assessment = (await db.prepare('SELECT * FROM assessments WHERE id = ?').bind(id).all()).results?.[0]
  if (!assessment) throw createError({ statusCode: 404, statusMessage: 'Not found' })
  if (user.role !== 'researcher' && assessment.teacher_id !== user.id) {
    throw createError({ statusCode: 403, statusMessage: 'Forbidden' })
  }

  const fields: string[] = []
  const values: any[] = []
  if (typeof body.title === 'string') {
    fields.push('title = ?')
    values.push(body.title)
  }
  if (typeof body.description === 'string') {
    fields.push('description = ?')
    values.push(body.description)
  }
  if (typeof body.is_validated === 'boolean') {
    if (user.role !== 'researcher') {
      throw createError({ statusCode: 403, statusMessage: 'Only researchers can validate' })
    }
    fields.push('is_validated = ?')
    values.push(body.is_validated ? 1 : 0)
  }
  if (fields.length === 0) return assessment

  fields.push('updated_at = CURRENT_TIMESTAMP')
  const sql = `UPDATE assessments SET ${fields.join(', ')} WHERE id = ?`
  values.push(id)
  await db.prepare(sql).bind(...values).run()

  const { results } = await db.prepare('SELECT * FROM assessments WHERE id = ?').bind(id).all()
  return results?.[0] ?? null
})


