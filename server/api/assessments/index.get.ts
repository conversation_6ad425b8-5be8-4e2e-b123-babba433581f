import { getDB } from '../../utils/db'

export default defineEventHandler(async (event) => {
  const user = (event as any).context.user
  if (!user) throw createError({ statusCode: 401, statusMessage: 'Unauthorized' })

  const db = getDB(event)
  let query = 'SELECT * FROM assessments'
  let bind: any[] = []
  if (user.role !== 'researcher') {
    query += ' WHERE teacher_id = ?'
    bind = [user.id]
  }

  const { results } = await db.prepare(query).bind(...bind).all()
  return results ?? []
})


