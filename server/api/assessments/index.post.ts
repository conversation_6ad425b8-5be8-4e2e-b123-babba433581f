import { getDB } from '../../utils/db'
import { logEvent } from '../../utils/logEvent'

export default defineEventHandler(async (event) => {
  const user = (event as any).context.user
  if (!user) throw createError({ statusCode: 401, statusMessage: 'Unauthorized' })

  const body = await readBody<{ title: string; description?: string; metadata?: any }>(event)
  if (!body?.title) throw createError({ statusCode: 400, statusMessage: 'Title required' })

  const id = crypto.randomUUID()
  const db = getDB(event)

  await db
    .prepare(
      'INSERT INTO assessments (id, teacher_id, title, description, metadata) VALUES (?, ?, ?, ?, ?)' 
    )
    .bind(id, user.id, body.title, body.description ?? null, JSON.stringify(body.metadata ?? null))
    .run()

  const { results } = await db.prepare('SELECT * FROM assessments WHERE id = ?').bind(id).all()
  await logEvent(event, 'ASSESSMENT_CREATE', { assessmentId: id, title: body.title })
  return send(event, results?.[0] ?? null, 201)
})


