import { getDB } from '../../../utils/db'

type NewQuestion = {
  question_text: string
  question_type?: string
  position: number
  metadata?: any
  options: Array<{ option_text: string; is_correct: boolean; explanation?: string }>
}

export default defineEventHandler(async (event) => {
  const user = (event as any).context.user
  if (!user) throw createError({ statusCode: 401, statusMessage: 'Unauthorized' })
  const id = getRouterParam(event, 'id')
  const body = await readBody<NewQuestion>(event)
  const db = getDB(event)

  const assessment = (await db.prepare('SELECT * FROM assessments WHERE id = ?').bind(id).all()).results?.[0]
  if (!assessment) throw createError({ statusCode: 404, statusMessage: 'Assessment not found' })
  if (user.role !== 'researcher' && assessment.teacher_id !== user.id) {
    throw createError({ statusCode: 403, statusMessage: 'Forbidden' })
  }

  const qid = crypto.randomUUID()
  const stmts = [
    db
      .prepare(
        'INSERT INTO questions (id, assessment_id, question_text, question_type, position, metadata) VALUES (?, ?, ?, ?, ?, ?)'
      )
      .bind(
        qid,
        id,
        body.question_text,
        body.question_type ?? 'multiple-choice',
        body.position,
        JSON.stringify(body.metadata ?? null)
      ),
  ]

  for (const opt of body.options) {
    stmts.push(
      db
        .prepare(
          'INSERT INTO options (id, question_id, option_text, is_correct, explanation) VALUES (?, ?, ?, ?, ?)'
        )
        .bind(crypto.randomUUID(), qid, opt.option_text, opt.is_correct ? 1 : 0, opt.explanation ?? null)
    )
  }

  await db.batch(stmts)

  const { results: question } = await db.prepare('SELECT * FROM questions WHERE id = ?').bind(qid).all()
  const { results: options } = await db.prepare('SELECT * FROM options WHERE question_id = ?').bind(qid).all()

  return send(event, { ...(question?.[0] || {}), options: options || [] }, 201)
})


