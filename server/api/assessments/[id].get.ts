import { getDB } from '../../utils/db'

export default defineEventHandler(async (event) => {
  const user = (event as any).context.user
  if (!user) throw createError({ statusCode: 401, statusMessage: 'Unauthorized' })

  const id = getRouterParam(event, 'id')
  const db = getDB(event)

  const assessment = (await db.prepare('SELECT * FROM assessments WHERE id = ?').bind(id).all()).results?.[0]
  if (!assessment) throw createError({ statusCode: 404, statusMessage: 'Not found' })
  if (user.role !== 'researcher' && assessment.teacher_id !== user.id) {
    throw createError({ statusCode: 403, statusMessage: 'Forbidden' })
  }

  const questions = (await db
    .prepare('SELECT * FROM questions WHERE assessment_id = ? ORDER BY position ASC')
    .bind(id)
    .all()).results ?? []

  const qIds = questions.map((q: any) => q.id)
  let optionsByQ: Record<string, any[]> = {}
  if (qIds.length) {
    const placeholders = qIds.map(() => '?').join(',')
    const allOptions = (await db
      .prepare(`SELECT * FROM options WHERE question_id IN (${placeholders})`)
      .bind(...qIds)
      .all()).results ?? []
    for (const opt of allOptions) {
      optionsByQ[opt.question_id] = optionsByQ[opt.question_id] || []
      optionsByQ[opt.question_id].push(opt)
    }
  }

  const nested = {
    ...assessment,
    questions: questions.map((q: any) => ({ ...q, options: optionsByQ[q.id] || [] })),
  }
  return nested
})


