import { logEvent } from '../../utils/logEvent'

export default defineEventHandler(async (event) => {
  const body = await readBody<{ context: { standard: string; topic: string; targeted_misconception: string } }>(event)
  if (!body?.context) {
    throw createError({ statusCode: 400, statusMessage: 'Missing context' })
  }

  const systemPrompt = `You are an expert in educational assessment design. Your task is to generate a single, high-quality multiple-choice question in valid JSON format. The JSON object must conform to this structure: { "question_text": string, "options": [ { "option_text": string, "is_correct": boolean, "explanation": string } ], "metadata": { "targeted_misconception": string } }.
- The question must assess the provided standard and topic.
- One option must be correct.
- The incorrect options (distractors) must be plausible and specifically target common student errors or the provided misconception.
- The explanation for each option should clarify why it is correct or incorrect.`

  const userPrompt = `Standard: ${body.context.standard}\nTopic: ${body.context.topic}\nTargeted Misconception: ${body.context.targeted_misconception}`

  const config = useRuntimeConfig()
  const aiBase = config.aiApiBase

  const payload = {
    model: 'viable-3',
    response_format: { type: 'json_object' },
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ],
  }

  const res = await $fetch<any>(`${aiBase}/chat/completions`, {
    method: 'POST',
    body: payload,
  })

  // OpenAI-compatible response
  const text = res?.choices?.[0]?.message?.content
  let json
  try {
    json = JSON.parse(text)
  } catch (e) {
    throw createError({ statusCode: 502, statusMessage: 'AI returned invalid JSON' })
  }

  await logEvent(event, 'AI_QUESTION_GENERATE', { prompt: { system: systemPrompt, user: userPrompt }, response: json })
  return json
})


