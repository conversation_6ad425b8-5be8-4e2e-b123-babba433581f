// Centralized utilities for API routes
import { initializeApp, cert, type App } from 'firebase-admin/app';
import { getAuth, type Auth } from 'firebase-admin/auth';

let adminApp: App | null = null;
let adminAuth: Auth | null = null;

function getFirebaseAdmin() {
  if (!adminApp) {
    adminApp = initializeApp({
      projectId: 'viablelearning'
    });
    adminAuth = getAuth(adminApp);
  }
  return adminAuth;
}

export async function verifyFirebaseToken(token: string) {
  try {
    const auth = getFirebaseAdmin();
    if (!auth) {
      throw new Error('Firebase admin not initialized');
    }
    
    const decodedToken = await auth.verifyIdToken(token);
    return decodedToken;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

export async function requireAuth(event: any) {
  const authHeader = getHeader(event, 'authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized - Missing or invalid authorization header'
    });
  }
  
  const token = authHeader.substring(7);
  const decodedToken = await verifyFirebaseToken(token);
  
  if (!decodedToken) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized - Invalid token'
    });
  }
  
  return decodedToken;
}

// Database utilities
export function getDB(event: any) {
  // In development, return simple in-memory mock with incremental IDs
  if (process.env.NODE_ENV === 'development') {
    let lastId = (globalThis as any).__MOCK_DB_LAST_ID__ ?? 0;
    const api = {
      prepare: (_sql?: string) => ({
        bind: (..._args: any[]) => ({
          first: () => null,
          all: () => ({ results: [] }),
          run: () => {
            lastId += 1;
            (globalThis as any).__MOCK_DB_LAST_ID__ = lastId;
            return { success: true, meta: { last_row_id: lastId } };
          }
        })
      })
    };
    return api;
  }
  
  return event.context.cloudflare?.env?.DB;
}

export function generateJoinCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export async function logEvent(db: any, userId: string | null, eventType: string, details: any = null) {
  try {
    await db.prepare(`
      INSERT INTO logs (user_id, event_type, details)
      VALUES (?, ?, ?)
    `).bind(userId, eventType, details ? JSON.stringify(details) : null).run();
  } catch (error) {
    console.error('Failed to log event:', error);
  }
}