import { getDB } from '../../utils/db'

export default defineEventHandler(async (event) => {
  const body = await readBody<{
    sessionId: string
    questionId: string
    selectedOptionId: string
    participantId: string
    responseTimeMs?: number
  }>(event)

  const db = getDB(event)
  // Ensure participant belongs to session
  const participant = (
    await db
      .prepare('SELECT * FROM participants WHERE id = ? AND session_id = ?')
      .bind(body.participantId, body.sessionId)
      .all()
  ).results?.[0]
  if (!participant) throw createError({ statusCode: 403, statusMessage: 'Invalid participant' })

  const correct = (
    await db
      .prepare('SELECT id FROM options WHERE question_id = ? AND is_correct = 1')
      .bind(body.questionId)
      .all()
  ).results?.[0]

  const isCorrect = correct?.id === body.selectedOptionId

  await db
    .prepare(
      'INSERT INTO responses (participant_id, session_id, question_id, selected_option_id, is_correct, response_time_ms) VALUES (?, ?, ?, ?, ?, ?)'
    )
    .bind(
      body.participantId,
      body.sessionId,
      body.questionId,
      body.selectedOptionId,
      isCorrect ? 1 : 0,
      body.responseTimeMs ?? null
    )
    .run()

  return send(event, { is_correct: isCorrect, correct_option_id: correct?.id }, 201)
})


