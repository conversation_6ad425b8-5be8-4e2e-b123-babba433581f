import { getDB, logEvent } from '../_utils';

export default defineEventHandler(async (event) => {
  try {
    const db = getDB(event);
    const body = await readBody(event);
    
    if (!db) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Database not available'
      });
    }
    
    if (!body.participant_id || !body.item_id || !body.response_data) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Participant ID, item ID, and response data are required'
      });
    }
    
    // Verify participant and item exist
    const participant = await db.prepare(`
      SELECT p.*, s.status as session_status
      FROM participants p
      JOIN sessions s ON p.session_id = s.id
      WHERE p.id = ?
    `).bind(body.participant_id).first();
    
    if (!participant) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Participant not found'
      });
    }
    
    if (participant.session_status !== 'active') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Session is not active'
      });
    }
    
    const item = await db.prepare(`
      SELECT * FROM items WHERE id = ?
    `).bind(body.item_id).first();
    
    if (!item) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Item not found'
      });
    }
    
    // Check if response already exists
    const existingResponse = await db.prepare(`
      SELECT id FROM responses 
      WHERE participant_id = ? AND item_id = ?
    `).bind(body.participant_id, body.item_id).first();
    
    if (existingResponse) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Response already submitted for this item'
      });
    }
    
    // Parse item content to check correct answer
    const itemContent = JSON.parse(item.content);
    const isCorrect = body.response_data.answer_index === itemContent.answer;
    
    // Save the response
    await db.prepare(`
      INSERT INTO responses (participant_id, item_id, response_data, is_correct)
      VALUES (?, ?, ?, ?)
    `).bind(
      body.participant_id,
      body.item_id,
      JSON.stringify(body.response_data),
      isCorrect
    ).run();
    
    await logEvent(db, null, 'RESPONSE_SUBMITTED', {
      participant_id: body.participant_id,
      item_id: body.item_id,
      is_correct: isCorrect
    });
    
    return {
      status: 'received',
      is_correct: isCorrect
    };
    
  } catch (error) {
    console.error('Submit response error:', error);
    throw error;
  }
});