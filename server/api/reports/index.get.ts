import { requireAuth, getDB } from '../_utils';

export default defineEventHandler(async (event) => {
  try {
    const decodedToken = await requireAuth(event);
    const db = getDB(event);

    if (!db) {
      throw createError({ statusCode: 500, statusMessage: 'Database not available' });
    }

    // List sessions for the authenticated user with related assessment info
    const result = await db.prepare(`
      SELECT 
        s.id as session_id,
        s.status,
        s.created_at,
        a.id as assessment_id,
        a.title as assessment_title,
        a.description
      FROM sessions s
      JOIN assessments a ON s.assessment_id = a.id
      WHERE a.user_id = ?
      ORDER BY s.created_at DESC
    `).bind(decodedToken.uid).all();

    return result.results || [];
  } catch (error) {
    console.error('List reports error:', error);
    throw error;
  }
});

