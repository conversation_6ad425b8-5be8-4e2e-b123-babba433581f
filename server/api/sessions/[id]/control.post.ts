import { getDB } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  const user = (event as any).context.user
  if (!user) throw createError({ statusCode: 401, statusMessage: 'Unauthorized' })

  const id = getRouterParam(event, 'id')
  const body = await readBody<{ action: 'start' | 'next_question' | 'end' }>(event)
  const db = getDB(event)

  const session = (await db.prepare('SELECT * FROM sessions WHERE id = ?').bind(id).all()).results?.[0]
  if (!session) throw createError({ statusCode: 404, statusMessage: 'Not found' })
  if (session.teacher_id !== user.id && user.role !== 'researcher') {
    throw createError({ statusCode: 403, statusMessage: 'Forbidden' })
  }

  if (body.action === 'start') {
    // set first question as current
    const firstQ = (await db
      .prepare('SELECT id FROM questions WHERE assessment_id = ? ORDER BY position ASC LIMIT 1')
      .bind(session.assessment_id)
      .all()).results?.[0]
    await db
      .prepare('UPDATE sessions SET status = "active", current_question_id = ? WHERE id = ?')
      .bind(firstQ?.id ?? null, id)
      .run()
  } else if (body.action === 'next_question') {
    const currentQ = session.current_question_id
    if (!currentQ) throw createError({ statusCode: 400, statusMessage: 'No current question' })
    const nextQ = (await db
      .prepare(
        'SELECT id FROM questions WHERE assessment_id = ? AND position > (SELECT position FROM questions WHERE id = ?) ORDER BY position ASC LIMIT 1'
      )
      .bind(session.assessment_id, currentQ)
      .all()).results?.[0]
    if (nextQ) {
      await db
        .prepare('UPDATE sessions SET current_question_id = ? WHERE id = ?')
        .bind(nextQ.id, id)
        .run()
    } else {
      await db.prepare('UPDATE sessions SET status = "finished" WHERE id = ?').bind(id).run()
    }
  } else if (body.action === 'end') {
    await db.prepare('UPDATE sessions SET status = "finished" WHERE id = ?').bind(id).run()
  }

  const updated = (await db.prepare('SELECT * FROM sessions WHERE id = ?').bind(id).all()).results?.[0]
  return { success: true, newState: updated?.status }
})


