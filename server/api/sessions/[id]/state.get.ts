import { getDB } from '../../../utils/db'

export default defineEventHandler(async (event) => {
  const id = getRouterParam(event, 'id')
  const db = getDB(event)
  const session = (await db.prepare('SELECT * FROM sessions WHERE id = ?').bind(id).all()).results?.[0]
  if (!session) throw createError({ statusCode: 404, statusMessage: 'Session not found' })

  if (session.status === 'waiting') {
    const participants = (
      await db.prepare('SELECT id, nickname FROM participants WHERE session_id = ?').bind(id).all()
    ).results
    return { status: 'waiting', participants }
  }

  if (session.status === 'active' && session.current_question_id) {
    const q = (
      await db.prepare('SELECT id, question_text FROM questions WHERE id = ?').bind(session.current_question_id).all()
    ).results?.[0]
    const options = (
      await db
        .prepare('SELECT id, option_text FROM options WHERE question_id = ?')
        .bind(session.current_question_id)
        .all()
    ).results
    return { status: 'active', question: { id: q.id, text: q.question_text, options }, question_start_time: session.updated_at ?? null }
  }

  if (session.status === 'finished') {
    const results = (
      await db
        .prepare(
          'SELECT question_id, COUNT(*) as count, SUM(CASE WHEN is_correct THEN 1 ELSE 0 END) as correct FROM responses WHERE session_id = ? GROUP BY question_id'
        )
        .bind(id)
        .all()
    ).results
    return { status: 'finished', results }
  }

  return { status: session.status }
})


