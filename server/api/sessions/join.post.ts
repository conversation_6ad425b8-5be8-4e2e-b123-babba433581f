import { getDB } from '../../utils/db'
import { logEvent } from '../../utils/logEvent'

export default defineEventHandler(async (event) => {
  const body = await readBody<{ join_code: string; nickname: string }>(event)
  if (!body?.join_code || !body?.nickname) {
    throw createError({ statusCode: 400, statusMessage: 'join_code and nickname required' })
  }

  const token = getHeader(event, 'authorization')?.split(' ')[1]
  if (!token) {
    throw createError({ statusCode: 401, statusMessage: 'Anonymous token required' })
  }

  // We do not verify roles here; students use anonymous auth. Middleware is not applied to this public route.
  const db = getDB(event)
  const session = (
    await db
      .prepare('SELECT * FROM sessions WHERE join_code = ? AND status = "waiting"')
      .bind(body.join_code.toUpperCase())
      .all()
  ).results?.[0]

  if (!session) throw createError({ statusCode: 404, statusMessage: 'Session not found or not joinable' })

  const participantId = crypto.randomUUID()
  // Trust client to pass valid anon uid inside token? For simplicity, bind token string; in production parse anon token similarly.
  await db
    .prepare(
      'INSERT INTO participants (id, session_id, firebase_anon_uid, nickname) VALUES (?, ?, ?, ?)'
    )
    .bind(participantId, session.id, token, body.nickname)
    .run()

  await logEvent(event, 'SESSION_JOIN', { sessionId: session.id, nickname: body.nickname })
  return { sessionId: session.id, participantId, nickname: body.nickname }
})


