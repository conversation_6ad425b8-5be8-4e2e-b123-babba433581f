import { defineStore } from 'pinia'
import { getAuth, onIdTokenChanged } from 'firebase/auth'

type UserProfile = { id: string; email?: string; display_name?: string; role: 'teacher' | 'researcher' }

export const useAuthStore = defineStore('auth', {
  state: () => ({
    idToken: '' as string,
    profile: null as UserProfile | null,
    ready: false,
  }),
  actions: {
    init() {
      const auth = getAuth()
      onIdTokenChanged(auth, async (user) => {
        this.idToken = (await user?.getIdToken?.()) || ''
        if (this.idToken) {
          // sync profile
          try {
            const res = await $fetch<UserProfile>('/api/auth/sync', {
              method: 'POST',
              headers: { Authorization: `Bearer ${this.idToken}` },
            })
            this.profile = res
          } catch {
            this.profile = null
          }
        } else {
          this.profile = null
        }
        this.ready = true
      })
    },
    async authorizedFetch<T>(url: string, opts: any = {}) {
      const headers = { ...(opts.headers || {}), Authorization: `Bearer ${this.idToken}` }
      return $fetch<T>(url, { ...opts, headers })
    },
  },
})


