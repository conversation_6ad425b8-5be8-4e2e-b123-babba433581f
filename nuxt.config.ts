// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },

  modules: ['@nuxtjs/tailwindcss'],

  css: ['~/assets/css/tailwind.css'],

  build: {
    transpile: ['shadcn-vue', 'radix-vue']
  },

  runtimeConfig: {
    public: {
      firebase: {
        apiKey: 'AIzaSyCYuNquZ-QhjCVqvmfZCXterbm-pU3jsoM',
        authDomain: 'viablelearning.firebaseapp.com',
        projectId: 'viablelearning',
        storageBucket: 'viablelearning.firebasestorage.app',
        messagingSenderId: '971830893184',
        appId: '1:971830893184:web:5e2892afd0b30749132465',
        measurementId: 'G-KZ00EYSW6L'
      }
    }
  },

  plugins: ['~/plugins/firebase.client.ts'],

  nitro: {
    preset: 'cloudflare-pages'
  }
})
