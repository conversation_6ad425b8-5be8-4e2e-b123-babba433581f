// https://nuxt.com/docs/api/configuration/nuxt-config
import { fileURLToPath } from 'node:url'
import { resolve } from 'node:path'

export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  modules: [
    '@nuxtjs/tailwindcss',
    '@nuxtjs/google-fonts'
  ],
  tailwindcss: {
    cssPath: '~/assets/css/tailwind.css'
  },
  components: [
    { path: '~/app/components', pathPrefix: false }
  ],
  css: [
    '@fortawesome/fontawesome-free/css/all.min.css',
    '~/assets/css/tailwind.css'
  ],
  app: {
    head: {
      titleTemplate: '%s · CHECKPOINT',
      title: 'CHECKPOINT',
      htmlAttrs: { lang: 'en' },
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { name: 'theme-color', content: '#0f172a' },
        { name: 'description', content: 'AI-powered diagnostic assessments for K-12' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    }
  },
  googleFonts: {
    families: {
      Poppins: [300, 400, 500, 600, 700]
    },
    display: 'swap'
  },
  alias: {
    '@shared': fileURLToPath(new URL('./shared', import.meta.url)),
    'shared': fileURLToPath(new URL('./shared', import.meta.url))
  },
  nitro: {
    preset: 'cloudflare-pages'
  },
  future: {
    typescriptBundlerResolution: true
  }
})
