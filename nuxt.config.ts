// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  modules: [
    '@nuxtjs/tailwindcss'
  ],
  css: [
    '@fortawesome/fontawesome-svg-core/styles.css',
    '~/assets/css/design-system.css'
  ],
  nitro: {
    // Configuration for Cloudflare Pages deployment
    preset: 'cloudflare-pages',
    experimental: {
      wasm: true
    }
  },
  runtimeConfig: {
    // Server-side secrets
    d1DatabaseId: '74020a9a-58ab-4bbd-9ec8-f5d44396f53',
    d1DatabaseName: 'viablelab-checkpoint',
    public: {
      // Client-side config
      firebaseConfig: {
        apiKey: "AIzaSyCYuNquZ-QhjCVqvmfZCXterbm-pU3jsoM",
        authDomain: "viablelearning.firebaseapp.com",
        projectId: "viablelearning",
        storageBucket: "viablelearning.firebasestorage.app",
        messagingSenderId: "971830893184",
        appId: "1:971830893184:web:5e2892afd0b30749132465",
        measurementId: "G-KZ00EYSW6L"
      }
    }
  },
  app: {
    head: {
      link: [
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: '' },
        { rel: 'stylesheet', href: 'https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap' }
      ]
    }
  }
})
