// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },

  modules: ['@nuxtjs/tailwindcss', '@pinia/nuxt'],

  css: ['~/assets/css/tailwind.css'],

  nitro: {
    preset: 'cloudflare-pages'
  },

  runtimeConfig: {
    aiApiBase: 'https://api.viablelab.org/v1',
    public: {
      firebase: {
        apiKey: 'AIzaSyCYuNquZ-QhjCVqvmfZCXterbm-pU3jsoM',
        authDomain: 'viablelearning.firebaseapp.com',
        projectId: 'viablelearning'
      }
    }
  },

  components: [
    {
      path: '~/components/ui',
      extensions: ['.vue'],
      prefix: ''
    },
    {
      path: '~/components',
      extensions: ['.vue']
    }
  ]
})
