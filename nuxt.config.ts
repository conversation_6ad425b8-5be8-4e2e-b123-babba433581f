// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  
  modules: [
    '@nuxt/ui',
    '@pinia/nuxt'
  ],

  css: [
    '@fortawesome/fontawesome-svg-core/styles.css'
  ],

  app: {
    head: {
      link: [
        {
          rel: 'preconnect',
          href: 'https://fonts.googleapis.com'
        },
        {
          rel: 'preconnect',
          href: 'https://fonts.gstatic.com',
          crossorigin: ''
        },
        {
          rel: 'stylesheet',
          href: 'https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap'
        }
      ]
    }
  },

  nitro: {
    preset: 'cloudflare-pages',
    experimental: {
      wasm: true
    }
  },

  runtimeConfig: {
    // Private keys (only available on the server-side)
    d1DatabaseId: '74020a9a-58ab-4bbd-9ec8-f5d44b396f53',
    
    // Public keys (exposed to the client-side)
    public: {
      firebaseConfig: {
        apiKey: "AIzaSyCYuNquZ-QhjCVqvmfZCXterbm-pU3jsoM",
        authDomain: "viablelearning.firebaseapp.com",
        projectId: "viablelearning",
        storageBucket: "viablelearning.firebasestorage.app",
        messagingSenderId: "971830893184",
        appId: "1:971830893184:web:5e2892afd0b30749132465",
        measurementId: "G-KZ00EYSW6L"
      },
      labApiUrl: 'https://api.viablelab.org/v1/chat/completions'
    }
  },

  tailwindcss: {
    config: {
      theme: {
        extend: {
          fontFamily: {
            'sans': ['Poppins', 'ui-sans-serif', 'system-ui'],
          },
          colors: {
            'brand': {
              'blue': {
                50: '#eff6ff',
                100: '#dbeafe',
                200: '#bfdbfe',
                300: '#93c5fd',
                400: '#60a5fa',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8',
                800: '#1e40af',
                900: '#1e3a8a',
                950: '#172554'
              },
              'orange': {
                50: '#fff7ed',
                100: '#ffedd5',
                200: '#fed7aa',
                300: '#fdba74',
                400: '#fb923c',
                500: '#f97316',
                600: '#ea580c',
                700: '#c2410c',
                800: '#9a3412',
                900: '#7c2d12',
                950: '#431407'
              }
            }
          }
        }
      }
    }
  }
})
