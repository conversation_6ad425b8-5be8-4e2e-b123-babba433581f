name = "viablelab-checkpoint"
main = "dist/server/index.mjs"
account_id = "9541e48ac63ee11dfb343cdf1d1482bc"
compatibility_date = "2025-08-12"

[[d1_databases]]
binding = "DB"
database_name = "viablelab-checkpoint"
database_id = "74020a9a-58ab-4bbd-9ec8-f5d44396f53"

[build]
command = "npm run build"

# For local development
[env.development]
[[env.development.d1_databases]]
binding = "DB"
database_name = "viablelab-checkpoint-dev"
database_id = "74020a9a-58ab-4bbd-9ec8-f5d44396f53"