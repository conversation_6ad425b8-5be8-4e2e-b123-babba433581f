{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "mcp__mcp-feedback__interactive_feedback", "Bash(npm run build:*)", "Bash(cp:*)", "Bash(grep:*)", "Bash(npm run:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(rmdir:*)", "<PERSON><PERSON>(true)", "Bash(rm:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(wrangler d1 create:*)", "Bash(npx wrangler d1 migrations create:*)", "Bash(npx drizzle-kit:*)", "Bash(npx wrangler d1 create:*)", "Bash(npx wrangler d1 execute:*)", "Bash(for file in server/api/assessments/[id]/index.get.ts server/api/assessments/[id]/report.get.ts server/api/assessments/[id]/index.put.ts)", "Bash(do)", "<PERSON><PERSON>(echo:*)", "Bash(done)", "Bash(for file in server/api/sessions/[id]/live.get.ts server/api/sessions/[id]/status.put.ts server/api/sessions/[code]/validate.get.ts)", "Bash(for file in server/api/auth/create-user.post.ts server/api/auth/sync-user.post.ts server/api/analytics/events.get.ts server/api/dashboard/stats.get.ts)", "Bash(for file in server/api/assessments/[id]/index.put.ts server/api/responses/submit.post.ts server/api/websocket.ws.ts server/api/sessions/join.post.ts server/api/sessions/[id]/status.put.ts server/api/analytics/export.get.ts)"], "deny": []}}