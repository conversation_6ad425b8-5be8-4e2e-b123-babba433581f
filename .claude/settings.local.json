{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "mcp__mcp-feedback__interactive_feedback", "Bash(npm run build:*)", "Bash(cp:*)", "Bash(grep:*)", "Bash(npm run:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(curl:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(rmdir:*)", "<PERSON><PERSON>(true)", "Bash(rm:*)", "Bash(ls:*)", "Bash(find:*)", "Bash(wrangler d1 create:*)"], "deny": []}}