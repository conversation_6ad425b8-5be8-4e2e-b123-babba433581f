# CHECKPOINT — AI-powered diagnostic assessments for K-12

Production-ready Nuxt 4 app for Cloudflare Pages.

- Poppins font, Font Awesome icons
- Three layouts: default, teacher, student
- LocalStorage-first data, Firebase Auth (email/Google for teachers, anonymous for students)
- Nuxt server API routes for viable-3 chat completions

Development

1. Install dependencies: `npm i`
2. Dev: `npm run dev`
3. Build: `npm run build`
4. Preview: `npm run preview`

Cloudflare Pages

- <PERSON><PERSON> preset configured (`cloudflare-pages`)
- Build command: `npm run build`
- Output dir: `.output/public`
