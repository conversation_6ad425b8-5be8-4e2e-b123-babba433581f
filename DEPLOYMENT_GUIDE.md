# CHECKPOINT Platform - Deployment Guide

## Overview
This guide covers deploying the CHECKPOINT platform to Cloudflare Pages with D1 database integration.

## Prerequisites
- Node.js 18+ and npm
- Wrangler CLI installed globally: `npm install -g wrangler`
- Cloudflare account with Pages and D1 access
- Firebase project configured

## Database Setup

### 1. Apply Database Migration
```bash
# Apply the initial schema to production D1
wrangler d1 execute viablelab-checkpoint --file=./migrations/0001_initial_schema.sql
```

### 2. Verify Database Structure
```bash
# List tables to verify setup
wrangler d1 execute viablelab-checkpoint --command="SELECT name FROM sqlite_master WHERE type='table';"
```

## Cloudflare Pages Deployment

### 1. Build Configuration
The project is already configured for Cloudflare Pages deployment with:
- Build command: `npm run build`
- Output directory: `dist`
- Node.js compatibility
- D1 database binding

### 2. Environment Variables
Set the following in your Cloudflare Pages dashboard:
- No environment variables needed (Firebase config is in code as specified)

### 3. Deploy via Git
1. Push your code to a GitHub repository
2. Connect the repository to Cloudflare Pages
3. Set build settings:
   - Framework preset: `Nuxt.js`
   - Build command: `npm run build`
   - Build output directory: `dist`
   - Root directory: `/`

### 4. D1 Database Binding
In your Cloudflare Pages settings, add D1 binding:
- Binding name: `DB`
- Database: `viablelab-checkpoint`
- Database ID: `74020a9a-58ab-4bbd-9ec8-f5d44396f53`

## Post-Deployment

### 1. Verify Firebase Configuration
- Ensure Firebase Auth is properly configured
- Test login functionality
- Verify API endpoints work correctly

### 2. Test Core Functionality
1. Teacher registration/login
2. Assessment creation
3. AI question generation
4. Session hosting
5. Student participation
6. Report generation

## Domain Setup
Configure your custom domain in Cloudflare Pages settings if desired.

## Monitoring
Monitor the application through:
- Cloudflare Analytics
- D1 database metrics
- Firebase Auth usage
- Application logs in Wrangler

## Troubleshooting

### Database Issues
```bash
# Check D1 connection
wrangler d1 execute viablelab-checkpoint --command="SELECT 1;"

# View recent logs
wrangler pages deployment tail
```

### Build Issues
- Verify Node.js version compatibility
- Check package.json dependencies
- Ensure Wrangler configuration is correct

## Security Notes
- Firebase configuration is embedded in the application as specified
- API routes use Firebase token verification
- D1 database queries use prepared statements
- CORS is handled automatically by Cloudflare Pages